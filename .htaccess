# حماية وتحسين موقع حكيم

# تفعيل إعادة الكتابة
RewriteEngine On

# حماية الملفات الحساسة
<Files "config/*.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "includes/*.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# حماية من عرض محتويات المجلدات
Options -Indexes

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # حماية من Clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # HTTPS Strict Transport Security
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

# حماية من هجمات SQL Injection
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# حد أقصى لحجم الملف المرفوع
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_only_cookies 1

# إخفاء أخطاء PHP في الإنتاج
php_flag display_errors Off
php_flag log_errors On

# إعادة توجيه الصفحات المفقودة
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# إعادة كتابة URLs الودية
RewriteRule ^dashboard/?$ dashboard/index.php [L]
RewriteRule ^patients/?$ pages/patients/index.php [L]
RewriteRule ^appointments/?$ pages/appointments/index.php [L]
RewriteRule ^prescriptions/?$ pages/prescriptions/index.php [L]
RewriteRule ^billing/?$ pages/billing/index.php [L]
RewriteRule ^reports/?$ pages/reports/index.php [L]

# إعادة توجيه HTTP إلى HTTPS (اختياري)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# منع الوصول المباشر لملفات PHP في مجلدات معينة
RewriteCond %{REQUEST_URI} ^/(config|includes)/.*\.php$
RewriteRule ^(.*)$ - [F,L]

<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$user = getCurrentUser();
$clinic_id = $_SESSION['clinic_id'];

// جلب الإحصائيات
$database = new Database();
$db = $database->getConnection();

// إحصائيات المرضى
$patients_query = "SELECT COUNT(*) as total, 
                   COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
                   FROM patients WHERE clinic_id = :clinic_id AND is_active = 1";
$patients_stmt = $db->prepare($patients_query);
$patients_stmt->bindParam(':clinic_id', $clinic_id);
$patients_stmt->execute();
$patients_stats = $patients_stmt->fetch();

// إحصائيات المواعيد
$appointments_query = "SELECT COUNT(*) as total,
                       COUNT(CASE WHEN appointment_date = CURDATE() THEN 1 END) as today,
                       COUNT(CASE WHEN appointment_date = CURDATE() AND status = 'scheduled' THEN 1 END) as pending_today
                       FROM appointments WHERE clinic_id = :clinic_id";
$appointments_stmt = $db->prepare($appointments_query);
$appointments_stmt->bindParam(':clinic_id', $clinic_id);
$appointments_stmt->execute();
$appointments_stats = $appointments_stmt->fetch();

// إحصائيات الزيارات
$visits_query = "SELECT COUNT(*) as total,
                 COUNT(CASE WHEN visit_date = CURDATE() THEN 1 END) as today,
                 SUM(CASE WHEN visit_date = CURDATE() THEN visit_cost ELSE 0 END) as today_revenue
                 FROM visits WHERE clinic_id = :clinic_id";
$visits_stmt = $db->prepare($visits_query);
$visits_stmt->bindParam(':clinic_id', $clinic_id);
$visits_stmt->execute();
$visits_stats = $visits_stmt->fetch();

// إحصائيات الفواتير
$invoices_query = "SELECT COUNT(*) as total,
                   SUM(total_amount) as total_amount,
                   SUM(paid_amount) as paid_amount,
                   COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
                   FROM invoices WHERE clinic_id = :clinic_id";
$invoices_stmt = $db->prepare($invoices_query);
$invoices_stmt->bindParam(':clinic_id', $clinic_id);
$invoices_stmt->execute();
$invoices_stats = $invoices_stmt->fetch();

// المواعيد القادمة
$upcoming_appointments_query = "SELECT a.*, p.first_name, p.last_name, u.full_name as doctor_name
                                FROM appointments a
                                JOIN patients p ON a.patient_id = p.id
                                JOIN users u ON a.doctor_id = u.id
                                WHERE a.clinic_id = :clinic_id 
                                AND a.appointment_date >= CURDATE()
                                AND a.status IN ('scheduled', 'confirmed')
                                ORDER BY a.appointment_date, a.appointment_time
                                LIMIT 5";
$upcoming_stmt = $db->prepare($upcoming_appointments_query);
$upcoming_stmt->bindParam(':clinic_id', $clinic_id);
$upcoming_stmt->execute();
$upcoming_appointments = $upcoming_stmt->fetchAll();

// الإشعارات الحديثة
$notifications = getUnreadNotifications($_SESSION['user_id']);

// تحديد اللغة
$lang = getCurrentLanguage();
$dir = $lang === 'ar' ? 'rtl' : 'ltr';
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - حكيم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <h4 class="text-white mb-0">
                <i class="fas fa-user-md me-2"></i>
                حكيم
            </h4>
            <small class="text-light"><?php echo $user['clinic_name']; ?></small>
        </div>
        
        <ul class="sidebar-nav nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../pages/patients/">
                    <i class="fas fa-users"></i>
                    المرضى
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../pages/appointments/">
                    <i class="fas fa-calendar-alt"></i>
                    المواعيد
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../pages/prescriptions/">
                    <i class="fas fa-prescription-bottle-alt"></i>
                    الوصفات الطبية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../pages/billing/">
                    <i class="fas fa-file-invoice-dollar"></i>
                    الفواتير
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../pages/reports/">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </li>
            <?php if ($user['role'] === 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link" href="../admin/">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </li>
            <?php endif; ?>
        </ul>
        
        <div class="mt-auto p-3">
            <div class="dropdown">
                <a class="nav-link text-white dropdown-toggle" href="#" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i>
                    <?php echo $user['full_name']; ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="../pages/profile.php">الملف الشخصي</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">مرحباً، <?php echo $user['full_name']; ?></h1>
                <p class="text-muted mb-0">إليك نظرة عامة على عيادتك اليوم</p>
            </div>
            
            <div class="d-flex align-items-center">
                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-primary position-relative" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (count($notifications) > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                            <?php echo count($notifications); ?>
                        </span>
                        <?php endif; ?>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notifications-container" style="width: 300px; max-height: 400px; overflow-y: auto;">
                        <h6 class="dropdown-header">الإشعارات</h6>
                        <?php if (empty($notifications)): ?>
                            <div class="dropdown-item-text text-center text-muted">
                                لا توجد إشعارات جديدة
                            </div>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                            <div class="dropdown-item">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1"><?php echo $notification['title']; ?></h6>
                                        <p class="mb-1 text-muted small"><?php echo $notification['message']; ?></p>
                                        <small class="text-muted"><?php echo formatArabicDate($notification['created_at']); ?></small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <button class="btn btn-primary d-md-none sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-md-6 col-xl-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo $patients_stats['total']; ?></div>
                            <div class="stats-label">إجمالي المرضى</div>
                            <small class="text-success">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo $patients_stats['today']; ?> اليوم
                            </small>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-xl-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo $appointments_stats['today']; ?></div>
                            <div class="stats-label">مواعيد اليوم</div>
                            <small class="text-warning">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo $appointments_stats['pending_today']; ?> في الانتظار
                            </small>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-xl-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo $visits_stats['today']; ?></div>
                            <div class="stats-label">زيارات اليوم</div>
                            <small class="text-info">
                                <i class="fas fa-shekel-sign me-1"></i>
                                <?php echo formatCurrency($visits_stats['today_revenue'] ?? 0); ?>
                            </small>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-stethoscope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-xl-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-number"><?php echo formatCurrency($invoices_stats['total_amount'] ?? 0); ?></div>
                            <div class="stats-label">إجمالي الفواتير</div>
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>
                                <?php echo formatCurrency($invoices_stats['paid_amount'] ?? 0); ?> مدفوع
                            </small>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-file-invoice-dollar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Upcoming Appointments -->
        <div class="row g-4">
            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="../pages/patients/add.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مريض جديد
                            </a>
                            <a href="../pages/appointments/add.php" class="btn btn-outline-success">
                                <i class="fas fa-calendar-plus me-2"></i>
                                حجز موعد
                            </a>
                            <a href="../pages/prescriptions/add.php" class="btn btn-outline-info">
                                <i class="fas fa-prescription me-2"></i>
                                كتابة وصفة طبية
                            </a>
                            <a href="../pages/billing/add.php" class="btn btn-outline-warning">
                                <i class="fas fa-file-invoice me-2"></i>
                                إنشاء فاتورة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Appointments -->
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-check me-2"></i>
                            المواعيد القادمة
                        </h5>
                        <a href="../pages/appointments/" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_appointments)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                <p>لا توجد مواعيد قادمة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>المريض</th>
                                            <th>الطبيب</th>
                                            <th>التاريخ</th>
                                            <th>الوقت</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $appointment['first_name'] . ' ' . $appointment['last_name']; ?></strong>
                                            </td>
                                            <td><?php echo $appointment['doctor_name']; ?></td>
                                            <td><?php echo formatArabicDate($appointment['appointment_date']); ?></td>
                                            <td><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'scheduled' => 'bg-warning',
                                                    'confirmed' => 'bg-success',
                                                    'completed' => 'bg-info',
                                                    'cancelled' => 'bg-danger'
                                                ];
                                                $status_texts = [
                                                    'scheduled' => 'مجدول',
                                                    'confirmed' => 'مؤكد',
                                                    'completed' => 'مكتمل',
                                                    'cancelled' => 'ملغي'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $status_classes[$appointment['status']]; ?>">
                                                    <?php echo $status_texts[$appointment['status']]; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
</body>
</html>

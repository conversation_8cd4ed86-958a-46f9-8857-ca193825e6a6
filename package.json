{"name": "hakim-clinic-management", "version": "1.0.0", "description": "نظام حكيم - نظام إدارة العيادات الذكي", "main": "assets/js/main.js", "scripts": {"build": "npm run build-css && npm run build-js", "build-css": "sass assets/scss/main.scss assets/css/style.css --style compressed", "build-js": "webpack --mode production", "dev": "npm run dev-css && npm run dev-js", "dev-css": "sass assets/scss/main.scss assets/css/style.css --watch", "dev-js": "webpack --mode development --watch", "lint": "eslint assets/js/**/*.js", "lint-fix": "eslint assets/js/**/*.js --fix", "test": "jest", "start": "php -S localhost:8000", "serve": "php -S localhost:8000 -t .", "optimize": "npm run build && npm run optimize-images", "optimize-images": "imagemin assets/images/**/*.{jpg,jpeg,png,svg} --out-dir=assets/images/optimized"}, "keywords": ["clinic", "medical", "management", "healthcare", "arabic", "php", "mysql", "bootstrap"], "author": "Hakim Development Team <<EMAIL>>", "license": "MIT", "homepage": "https://xyz.collectandwin.xyz", "repository": {"type": "git", "url": "https://github.com/hakim-clinic/hakim.git"}, "bugs": {"url": "https://github.com/hakim-clinic/hakim/issues"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "babel-loader": "^9.1.0", "css-loader": "^6.7.0", "eslint": "^8.30.0", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "jest": "^29.3.0", "sass": "^1.57.0", "style-loader": "^3.3.0", "webpack": "^5.75.0", "webpack-cli": "^5.0.0"}, "dependencies": {"bootstrap": "^5.3.0", "@fortawesome/fontawesome-free": "^6.4.0", "aos": "^2.3.4", "chart.js": "^4.2.0", "fullcalendar": "^6.0.0", "sweetalert2": "^11.6.0", "datatables.net": "^1.13.0", "datatables.net-bs5": "^1.13.0", "select2": "^4.1.0", "moment": "^2.29.0", "flatpickr": "^4.6.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}
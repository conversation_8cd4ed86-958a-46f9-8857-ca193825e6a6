<?php
// ملف الدوال المساعدة

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للحصول على بيانات المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT u.*, c.name as clinic_name FROM users u 
              LEFT JOIN clinics c ON u.clinic_id = c.id 
              WHERE u.id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();
    
    return $stmt->fetch();
}

// دالة للتحقق من الصلاحيات
function hasPermission($required_role) {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }
    
    $roles = ['secretary' => 1, 'doctor' => 2, 'admin' => 3];
    $user_level = $roles[$user['role']] ?? 0;
    $required_level = $roles[$required_role] ?? 0;
    
    return $user_level >= $required_level;
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = $alertClass[$type] ?? 'alert-info';
    
    echo "<div class='alert $class alert-dismissible fade show' role='alert'>
            $message
            <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
          </div>";
}

// دالة لتوليد رقم المريض
function generatePatientNumber($clinic_id) {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT COUNT(*) as count FROM patients WHERE clinic_id = :clinic_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':clinic_id', $clinic_id);
    $stmt->execute();
    $result = $stmt->fetch();
    
    $count = $result['count'] + 1;
    return 'P' . str_pad($clinic_id, 3, '0', STR_PAD_LEFT) . str_pad($count, 4, '0', STR_PAD_LEFT);
}

// دالة لتوليد رقم الفاتورة
function generateInvoiceNumber($clinic_id) {
    $database = new Database();
    $db = $database->getConnection();
    
    $year = date('Y');
    $query = "SELECT COUNT(*) as count FROM invoices 
              WHERE clinic_id = :clinic_id AND YEAR(invoice_date) = :year";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':clinic_id', $clinic_id);
    $stmt->bindParam(':year', $year);
    $stmt->execute();
    $result = $stmt->fetch();
    
    $count = $result['count'] + 1;
    return 'INV-' . $year . '-' . str_pad($clinic_id, 3, '0', STR_PAD_LEFT) . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
}

// دالة لحساب العمر
function calculateAge($birthdate) {
    $birth = new DateTime($birthdate);
    $today = new DateTime();
    $age = $today->diff($birth);
    return $age->y;
}

// دالة لتنسيق التاريخ العربي
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

// دالة لرفع الملفات
function uploadFile($file, $destination_folder = 'uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'خطأ في الملف'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $destination_folder . $new_filename;
    
    if (!is_dir($destination_folder)) {
        mkdir($destination_folder, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'success' => true,
            'filename' => $new_filename,
            'path' => $upload_path,
            'size' => $file['size']
        ];
    } else {
        return ['success' => false, 'message' => 'فشل في حفظ الملف'];
    }
}

// دالة لإرسال الإشعارات
function sendNotification($clinic_id, $user_id, $type, $title, $message, $scheduled_for = null) {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "INSERT INTO notifications (clinic_id, user_id, type, title, message, scheduled_for) 
              VALUES (:clinic_id, :user_id, :type, :title, :message, :scheduled_for)";
    $stmt = $db->prepare($query);
    
    $stmt->bindParam(':clinic_id', $clinic_id);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':type', $type);
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':message', $message);
    $stmt->bindParam(':scheduled_for', $scheduled_for);
    
    return $stmt->execute();
}

// دالة للحصول على الإشعارات غير المقروءة
function getUnreadNotifications($user_id) {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT * FROM notifications 
              WHERE user_id = :user_id AND is_read = FALSE 
              ORDER BY created_at DESC";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    
    return $stmt->fetchAll();
}

// دالة لتحديد حالة الاشتراك
function getSubscriptionStatus($clinic_id) {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT subscription_plan, subscription_expires FROM clinics WHERE id = :clinic_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':clinic_id', $clinic_id);
    $stmt->execute();
    $clinic = $stmt->fetch();
    
    if (!$clinic) {
        return ['status' => 'inactive', 'plan' => null, 'expires' => null];
    }
    
    $expires = new DateTime($clinic['subscription_expires']);
    $today = new DateTime();
    
    if ($expires < $today) {
        return ['status' => 'expired', 'plan' => $clinic['subscription_plan'], 'expires' => $clinic['subscription_expires']];
    } elseif ($expires->diff($today)->days <= 7) {
        return ['status' => 'expiring_soon', 'plan' => $clinic['subscription_plan'], 'expires' => $clinic['subscription_expires']];
    } else {
        return ['status' => 'active', 'plan' => $clinic['subscription_plan'], 'expires' => $clinic['subscription_expires']];
    }
}

// دالة لتسجيل العمليات
function logActivity($user_id, $action, $details = null) {
    $database = new Database();
    $db = $database->getConnection();
    
    // إنشاء جدول السجلات إذا لم يكن موجوداً
    $create_table = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $db->exec($create_table);
    
    $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) 
              VALUES (:user_id, :action, :details, :ip_address, :user_agent)";
    $stmt = $db->prepare($query);
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':action', $action);
    $stmt->bindParam(':details', $details);
    $stmt->bindParam(':ip_address', $ip_address);
    $stmt->bindParam(':user_agent', $user_agent);
    
    return $stmt->execute();
}

// دالة للتحقق من حدود الاشتراك
function checkSubscriptionLimits($clinic_id, $type) {
    $subscription = getSubscriptionStatus($clinic_id);
    if ($subscription['status'] !== 'active') {
        return false;
    }
    
    $plan = SUBSCRIPTION_PLANS[$subscription['plan']];
    $database = new Database();
    $db = $database->getConnection();
    
    switch ($type) {
        case 'patients':
            if ($plan['features']['patients'] === -1) return true;
            $query = "SELECT COUNT(*) as count FROM patients WHERE clinic_id = :clinic_id";
            break;
        case 'users':
            if ($plan['features']['users'] === -1) return true;
            $query = "SELECT COUNT(*) as count FROM users WHERE clinic_id = :clinic_id";
            break;
        default:
            return true;
    }
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':clinic_id', $clinic_id);
    $stmt->execute();
    $result = $stmt->fetch();
    
    return $result['count'] < $plan['features'][$type];
}

// دالة لتوليد تقرير PDF
function generatePDFReport($data, $template, $filename) {
    // هذه الدالة تحتاج إلى مكتبة PDF مثل TCPDF أو FPDF
    // سيتم تنفيذها لاحقاً
    return false;
}
?>

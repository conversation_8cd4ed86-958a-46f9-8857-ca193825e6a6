# نظام حكيم - إدارة العيادات الذكي

## نظرة عامة
حكيم هو نظام شامل لإدارة العيادات الطبية مصمم خصيصاً للأطباء والعيادات في المنطقة العربية. يوفر النظام جميع الأدوات اللازمة لإدارة المرضى، المواعيد، الوصفات الطبية، والفواتير بطريقة احترافية وآمنة.

## المميزات الرئيسية

### 🏥 إدارة العيادات
- إدارة متعددة العيادات والفروع
- نظام صلاحيات متقدم (مدير، طبيب، سكرتيرة)
- لوحة تحكم شاملة مع إحصائيات مفصلة

### 👥 إدارة المرضى
- ملفات مرضى إلكترونية شاملة
- سجل طبي متكامل
- إمكانية رفع الملفات والتقارير الطبية
- نظام بحث متقدم

### 📅 نظام المواعيد
- تقويم ذكي للمواعيد
- إشعارات تلقائية للمرضى والأطباء
- إدارة قوائم الانتظار
- تتبع حالة المواعيد

### 💊 الوصفات الطبية
- كتابة وصفات إلكترونية
- قاعدة بيانات أدوية شاملة
- طباعة وصفات احترافية
- حفظ الوصفات السابقة

### 💰 نظام الفواتير
- إنشاء فواتير احترافية
- تتبع المدفوعات
- تقارير مالية مفصلة
- دعم عملة الشيكل الفلسطيني

### 📊 التقارير والإحصائيات
- تقارير يومية وشهرية وسنوية
- إحصائيات المرضى والزيارات
- تحليل الأداء المالي
- تصدير التقارير بصيغة PDF

## المتطلبات التقنية

### متطلبات الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache أو Nginx
- SSL Certificate (مستحسن)

### المكتبات المطلوبة
- PDO MySQL Extension
- GD Extension (للصور)
- cURL Extension
- OpenSSL Extension

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/hakim-clinic.git
cd hakim-clinic
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. عدّل ملف `config/database.php` بمعلومات قاعدة البيانات:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hakim_clinic_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. تشغيل التثبيت
1. ارفع الملفات إلى الخادم
2. اذهب إلى `https://yourdomain.com/install.php`
3. اتبع تعليمات التثبيت

### 4. إعداد الصلاحيات
تأكد من أن المجلدات التالية قابلة للكتابة:
```bash
chmod 755 uploads/
chmod 755 assets/
```

## الحسابات الافتراضية

بعد التثبيت، ستجد الحسابات التالية:

### مدير النظام
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### طبيب
- **اسم المستخدم:** doctor
- **كلمة المرور:** doctor123

### سكرتيرة
- **اسم المستخدم:** secretary
- **كلمة المرور:** secretary123

> ⚠️ **تحذير:** يرجى تغيير كلمات المرور الافتراضية فوراً بعد التثبيت

## الإعدادات

### إعدادات البريد الإلكتروني
عدّل ملف `config/config.php` لإعداد SMTP:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### إعدادات الدفع
لتفعيل PayPal، عدّل الإعدادات في `config/config.php`:
```php
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
```

## الاستخدام

### للأطباء
1. سجل دخولك باستخدام حساب الطبيب
2. أضف مرضى جدد من قسم "المرضى"
3. احجز مواعيد من قسم "المواعيد"
4. اكتب وصفات طبية من قسم "الوصفات"
5. راجع التقارير والإحصائيات

### للسكرتيرة
1. إدارة المواعيد والحجوزات
2. استقبال المرضى وتحديث بياناتهم
3. إنشاء الفواتير وتتبع المدفوعات
4. طباعة التقارير اليومية

### للمدير
1. إدارة المستخدمين والصلاحيات
2. مراجعة التقارير المالية
3. إعداد النظام والعيادة
4. إدارة الاشتراكات

## الأمان

### حماية البيانات
- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- تنظيف البيانات المدخلة
- جلسات آمنة

### النسخ الاحتياطي
يُنصح بعمل نسخ احتياطية دورية:
```bash
# نسخة احتياطية لقاعدة البيانات
mysqldump -u username -p hakim_clinic_db > backup.sql

# نسخة احتياطية للملفات
tar -czf hakim_backup.tar.gz /path/to/hakim/
```

## الدعم الفني

### المشاكل الشائعة

**مشكلة الاتصال بقاعدة البيانات:**
- تحقق من معلومات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشكلة رفع الملفات:**
- تحقق من صلاحيات مجلد `uploads/`
- تأكد من إعدادات PHP للحد الأقصى لحجم الملف

**مشكلة الجلسات:**
- تحقق من إعدادات PHP session
- تأكد من صلاحيات مجلد session

### التواصل
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://hakim-clinic.com
- **الدعم الفني:** متوفر 24/7

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## التحديثات

### الإصدار 1.0.0
- إطلاق النسخة الأولى
- جميع المميزات الأساسية
- دعم اللغة العربية والإنجليزية
- نظام الفواتير والمدفوعات

### خطط مستقبلية
- تطبيق موبايل
- تكامل مع أنظمة المختبرات
- ذكاء اصطناعي للتشخيص
- تقارير متقدمة

---

**حكيم - نظام إدارة العيادات الذكي**  
*مصمم خصيصاً للأطباء العرب*

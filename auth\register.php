<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    redirect('../dashboard/index.php');
}

$error_message = '';
$success_message = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من CSRF Token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان';
    } else {
        $clinic_name = sanitizeInput($_POST['clinic_name'] ?? '');
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $terms_accepted = isset($_POST['terms_accepted']);
        
        // التحقق من البيانات
        if (empty($clinic_name) || empty($full_name) || empty($email) || empty($username) || empty($password)) {
            $error_message = 'يرجى ملء جميع الحقول المطلوبة';
        } elseif (!isValidEmail($email)) {
            $error_message = 'البريد الإلكتروني غير صحيح';
        } elseif ($password !== $confirm_password) {
            $error_message = 'كلمات المرور غير متطابقة';
        } elseif (!isStrongPassword($password)) {
            $error_message = 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل وتشمل أحرف كبيرة وصغيرة وأرقام';
        } elseif (!$terms_accepted) {
            $error_message = 'يجب الموافقة على الشروط والأحكام';
        } else {
            $database = new Database();
            $db = $database->getConnection();
            
            // التحقق من عدم وجود المستخدم أو البريد الإلكتروني
            $check_query = "SELECT id FROM users WHERE username = :username OR email = :email";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->bindParam(':username', $username);
            $check_stmt->bindParam(':email', $email);
            $check_stmt->execute();
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل';
            } else {
                try {
                    $db->beginTransaction();
                    
                    // إنشاء العيادة
                    $clinic_query = "INSERT INTO clinics (name, phone, email, subscription_plan, subscription_expires, is_active) 
                                    VALUES (:name, :phone, :email, 'basic', DATE_ADD(NOW(), INTERVAL 7 DAY), 1)";
                    $clinic_stmt = $db->prepare($clinic_query);
                    $clinic_stmt->bindParam(':name', $clinic_name);
                    $clinic_stmt->bindParam(':phone', $phone);
                    $clinic_stmt->bindParam(':email', $email);
                    $clinic_stmt->execute();
                    
                    $clinic_id = $db->lastInsertId();
                    
                    // إنشاء المستخدم
                    $hashed_password = hashPassword($password);
                    $user_query = "INSERT INTO users (username, email, password, full_name, phone, role, clinic_id, is_active) 
                                  VALUES (:username, :email, :password, :full_name, :phone, 'admin', :clinic_id, 1)";
                    $user_stmt = $db->prepare($user_query);
                    $user_stmt->bindParam(':username', $username);
                    $user_stmt->bindParam(':email', $email);
                    $user_stmt->bindParam(':password', $hashed_password);
                    $user_stmt->bindParam(':full_name', $full_name);
                    $user_stmt->bindParam(':phone', $phone);
                    $user_stmt->bindParam(':clinic_id', $clinic_id);
                    $user_stmt->execute();
                    
                    $user_id = $db->lastInsertId();
                    
                    // إرسال إشعار ترحيب
                    sendNotification($clinic_id, $user_id, 'system_alert', 'مرحباً بك في حكيم', 
                                   'تم إنشاء حسابك بنجاح. يمكنك الآن البدء في استخدام النظام.');
                    
                    $db->commit();
                    
                    // تسجيل النشاط
                    logActivity($user_id, 'register', 'تسجيل حساب جديد');
                    
                    // إعادة التوجيه لصفحة تسجيل الدخول
                    redirect('login.php?registered=1');
                    
                } catch (Exception $e) {
                    $db->rollBack();
                    $error_message = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
                    error_log('Registration error: ' . $e->getMessage());
                }
            }
        }
    }
}

// تحديد اللغة
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'ar';
$dir = $lang === 'ar' ? 'rtl' : 'ltr';

$texts = [
    'ar' => [
        'title' => 'إنشاء حساب - حكيم',
        'register' => 'إنشاء حساب جديد',
        'clinic_name' => 'اسم العيادة',
        'full_name' => 'الاسم الكامل',
        'email' => 'البريد الإلكتروني',
        'phone' => 'رقم الهاتف',
        'username' => 'اسم المستخدم',
        'password' => 'كلمة المرور',
        'confirm_password' => 'تأكيد كلمة المرور',
        'terms_accepted' => 'أوافق على الشروط والأحكام',
        'create_account' => 'إنشاء الحساب',
        'have_account' => 'لديك حساب بالفعل؟',
        'login_now' => 'سجل دخولك',
        'back_home' => 'العودة للرئيسية',
        'free_trial' => 'تجربة مجانية لمدة 7 أيام'
    ],
    'en' => [
        'title' => 'Register - Hakim',
        'register' => 'Create New Account',
        'clinic_name' => 'Clinic Name',
        'full_name' => 'Full Name',
        'email' => 'Email',
        'phone' => 'Phone Number',
        'username' => 'Username',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'terms_accepted' => 'I agree to Terms and Conditions',
        'create_account' => 'Create Account',
        'have_account' => 'Already have an account?',
        'login_now' => 'Login Now',
        'back_home' => 'Back to Home',
        'free_trial' => '7 Days Free Trial'
    ]
];

$t = $texts[$lang];
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $t['title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #60a5fa;
            --success-color: #16a34a;
            --text-white: #ffffff;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-700: #334155;
            --gray-900: #0f172a;
        }

        body {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 50px 50px;
            animation: float 8s ease-in-out infinite;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
            z-index: 2;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            max-width: 600px;
            width: 100%;
        }

        .register-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--primary-light));
        }

        .register-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            background: transparent;
        }

        .register-header h2 {
            color: var(--gray-900);
            font-weight: 800;
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }

        .register-header p {
            color: var(--gray-700);
            font-size: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .register-header small {
            color: var(--success-color);
            font-weight: 600;
            background: rgba(22, 163, 74, 0.1);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            display: inline-block;
        }

        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--gray-100);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            background: white;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 6px;
            border: 2px solid var(--gray-300);
            transition: all 0.3s ease;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--gray-700);
            margin-right: 0.75rem;
            cursor: pointer;
        }

        .password-strength {
            height: 6px;
            border-radius: 3px;
            transition: all 0.3s ease;
            background: var(--gray-200);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .text-center a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .text-center a:hover {
            color: var(--primary-dark);
            transform: translateY(-1px);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
            .register-container {
                padding: 1rem;
            }

            .register-card {
                margin: 0;
                border-radius: 20px;
            }

            .register-header h2 {
                font-size: 1.5rem;
            }

            .register-header {
                padding: 1.5rem 1.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="register-card">
                        <!-- Header -->
                        <div class="register-header text-center p-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-md me-2"></i>
                                حكيم
                            </h2>
                            <p class="mb-0 mt-2"><?php echo $t['register']; ?></p>
                            <small class="text-light">
                                <i class="fas fa-gift me-1"></i>
                                <?php echo $t['free_trial']; ?>
                            </small>
                        </div>
                        
                        <!-- Body -->
                        <div class="p-4">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="clinic_name" class="form-label">
                                            <i class="fas fa-hospital me-2"></i>
                                            <?php echo $t['clinic_name']; ?> *
                                        </label>
                                        <input type="text" class="form-control" id="clinic_name" name="clinic_name" 
                                               value="<?php echo htmlspecialchars($_POST['clinic_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم العيادة
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            <i class="fas fa-user me-2"></i>
                                            <?php echo $t['full_name']; ?> *
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال الاسم الكامل
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope me-2"></i>
                                            <?php echo $t['email']; ?> *
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال بريد إلكتروني صحيح
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone me-2"></i>
                                            <?php echo $t['phone']; ?>
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-at me-2"></i>
                                        <?php echo $t['username']; ?> *
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                    <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم مستخدم صحيح
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock me-2"></i>
                                            <?php echo $t['password']; ?> *
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                                <i class="fas fa-eye" id="password-toggle-icon"></i>
                                            </button>
                                        </div>
                                        <div class="password-strength mt-1" id="password-strength"></div>
                                        <div class="form-text">8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام</div>
                                        <div class="invalid-feedback">
                                            كلمة المرور غير قوية بما فيه الكفاية
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">
                                            <i class="fas fa-lock me-2"></i>
                                            <?php echo $t['confirm_password']; ?> *
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                                <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">
                                            كلمات المرور غير متطابقة
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <div class="form-check p-3 border rounded-3" style="background: rgba(37, 99, 235, 0.05); border-color: var(--primary-color) !important;">
                                        <input type="checkbox" class="form-check-input" id="terms_accepted" name="terms_accepted" required>
                                        <label class="form-check-label" for="terms_accepted">
                                            <strong><?php echo $t['terms_accepted']; ?> *</strong>
                                            <br>
                                            <small class="text-muted">
                                                بالموافقة، أنت تقر بأنك قرأت وفهمت
                                                <a href="#" class="text-primary">الشروط والأحكام</a> و
                                                <a href="#" class="text-primary">سياسة الخصوصية</a>
                                            </small>
                                        </label>
                                        <div class="invalid-feedback">
                                            يجب الموافقة على الشروط والأحكام للمتابعة
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>
                                        <?php echo $t['create_account']; ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Footer -->
                        <div class="text-center p-4 border-top">
                            <p class="mb-2"><?php echo $t['have_account']; ?></p>
                            <a href="login.php" class="btn btn-outline-primary">
                                <?php echo $t['login_now']; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-right me-2"></i>
                            <?php echo $t['back_home']; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // فحص قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('password-strength');
            const strength = checkPasswordStrength(password);
            
            strengthBar.className = 'password-strength mt-1 bg-' + strength.color;
            strengthBar.style.width = strength.width + '%';
        });
        
        function checkPasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;
            
            switch (score) {
                case 0:
                case 1:
                    return { color: 'danger', width: 20 };
                case 2:
                    return { color: 'warning', width: 40 };
                case 3:
                    return { color: 'info', width: 60 };
                case 4:
                    return { color: 'primary', width: 80 };
                case 5:
                    return { color: 'success', width: 100 };
                default:
                    return { color: 'danger', width: 20 };
            }
        }
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // التحقق من النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>

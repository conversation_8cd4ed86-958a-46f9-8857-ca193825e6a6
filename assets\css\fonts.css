/* ملف الخطوط العربية المحسنة لموقع حكيم */

/* استير<PERSON> خطوط Google Fonts العربية المحسنة */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;500;600;700&display=swap');

/* تعريف الخطوط المخصصة */
:root {
    /* الخط الأساسي للنصوص العادية */
    --font-primary: 'Tajawal', 'Cairo', 'Almarai', 'Segoe UI', sans-serif;
    
    /* خط العناوين */
    --font-headings: 'Cairo', 'Tajawal', 'Almarai', sans-serif;
    
    /* خط النصوص الفاخرة */
    --font-elegant: 'Amiri', 'Scheherazade New', serif;
    
    /* خط الأرقام والبيانات */
    --font-numbers: 'Cairo', 'Tajawal', monospace;
    
    /* أوزان الخطوط */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
}

/* تطبيق الخطوط على العناصر */
body {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-normal);
    line-height: 1.7;
    letter-spacing: 0.01em;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-headings);
    font-weight: var(--font-weight-bold);
    line-height: 1.3;
    letter-spacing: -0.01em;
}

h1 {
    font-size: 3rem;
    font-weight: var(--font-weight-black);
}

h2 {
    font-size: 2.5rem;
    font-weight: var(--font-weight-extrabold);
}

h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
}

h4 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-medium);
}

h6 {
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
}

/* النصوص الخاصة */
.text-elegant {
    font-family: var(--font-elegant);
    font-weight: var(--font-weight-normal);
    line-height: 1.8;
}

.text-numbers {
    font-family: var(--font-numbers);
    font-variant-numeric: tabular-nums;
}

/* أحجام النصوص */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

.text-base {
    font-size: 1rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.text-5xl {
    font-size: 3rem;
}

/* أوزان النصوص */
.font-light {
    font-weight: var(--font-weight-light);
}

.font-normal {
    font-weight: var(--font-weight-normal);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

.font-extrabold {
    font-weight: var(--font-weight-extrabold);
}

.font-black {
    font-weight: var(--font-weight-black);
}

/* تحسينات خاصة بالعربية */
.arabic-text {
    font-family: var(--font-primary);
    direction: rtl;
    text-align: right;
    line-height: 1.8;
    word-spacing: 0.1em;
}

.arabic-heading {
    font-family: var(--font-headings);
    direction: rtl;
    text-align: right;
    line-height: 1.4;
    font-weight: var(--font-weight-bold);
}

.arabic-elegant {
    font-family: var(--font-elegant);
    direction: rtl;
    text-align: right;
    line-height: 2;
    font-size: 1.125rem;
}

/* تحسينات للقراءة */
.readable-text {
    font-size: 1.125rem;
    line-height: 1.8;
    letter-spacing: 0.02em;
    word-spacing: 0.1em;
}

.compact-text {
    line-height: 1.5;
    letter-spacing: -0.01em;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    h3 {
        font-size: 1.75rem;
    }
    
    h4 {
        font-size: 1.375rem;
    }
    
    body {
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    .readable-text {
        font-size: 1rem;
        line-height: 1.7;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    h3 {
        font-size: 1.5rem;
    }
    
    body {
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    body {
        font-family: var(--font-primary);
        font-size: 12pt;
        line-height: 1.5;
        color: black;
    }
    
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--font-headings);
        color: black;
        page-break-after: avoid;
    }
    
    h1 {
        font-size: 18pt;
    }
    
    h2 {
        font-size: 16pt;
    }
    
    h3 {
        font-size: 14pt;
    }
    
    h4, h5, h6 {
        font-size: 12pt;
    }
}

/* تحسينات الأداء */
.font-display-swap {
    font-display: swap;
}

/* تحسين عرض الخطوط */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* دعم الخطوط المتغيرة */
@supports (font-variation-settings: normal) {
    .variable-font {
        font-variation-settings: 'wght' 400;
        transition: font-variation-settings 0.3s ease;
    }
    
    .variable-font:hover {
        font-variation-settings: 'wght' 600;
    }
}

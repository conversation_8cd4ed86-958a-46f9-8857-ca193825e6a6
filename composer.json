{"name": "hakim/clinic-management", "description": "نظام حكيم - نظام إدارة العيادات الذكي", "type": "project", "keywords": ["clinic", "medical", "management", "arabic", "healthcare"], "homepage": "https://xyz.collectandwin.xyz", "license": "MIT", "authors": [{"name": "Hakim Development Team", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=7.4.0", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-gd": "*", "ext-curl": "*", "ext-openssl": "*", "ext-json": "*", "ext-mbstring": "*", "tcpdf/tcpdf": "^6.4", "phpmailer/phpmailer": "^6.6", "vlucas/phpdotenv": "^5.4"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.6", "phpstan/phpstan": "^1.8"}, "autoload": {"psr-4": {"Hakim\\": "src/", "Hakim\\Controllers\\": "src/Controllers/", "Hakim\\Models\\": "src/Models/", "Hakim\\Services\\": "src/Services/", "Hakim\\Utils\\": "src/Utils/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"Hakim\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyze": "phpstan analyse src/", "install-db": "php install.php", "start": "php start.php", "backup": "php scripts/backup.php", "migrate": "php scripts/migrate.php"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "support": {"email": "<EMAIL>", "issues": "https://github.com/hakim-clinic/issues", "docs": "https://docs.hakim-clinic.com"}}
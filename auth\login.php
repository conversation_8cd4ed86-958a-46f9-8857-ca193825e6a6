<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    redirect('../dashboard/index.php');
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "SELECT u.*, c.name as clinic_name, c.is_active as clinic_active 
                  FROM users u 
                  LEFT JOIN clinics c ON u.clinic_id = c.id 
                  WHERE (u.username = :username OR u.email = :username) AND u.is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if ($user && verifyPassword($password, $user['password'])) {
            // التحقق من حالة العيادة
            if ($user['clinic_id'] && !$user['clinic_active']) {
                $error_message = 'العيادة غير نشطة. يرجى التواصل مع الإدارة';
            } else {
                // تسجيل الدخول بنجاح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['clinic_id'] = $user['clinic_id'];
                $_SESSION['clinic_name'] = $user['clinic_name'];
                
                // تسجيل النشاط
                logActivity($user['id'], 'login', 'تسجيل دخول ناجح');
                
                // تذكر المستخدم
                if ($remember_me) {
                    $token = generateRandomToken();
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    
                    // حفظ التوكن في قاعدة البيانات
                    $update_query = "UPDATE users SET remember_token = :token WHERE id = :user_id";
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->bindParam(':token', $token);
                    $update_stmt->bindParam(':user_id', $user['id']);
                    $update_stmt->execute();
                }
                
                // إعادة التوجيه
                $redirect_url = $_GET['redirect'] ?? '../dashboard/index.php';
                redirect($redirect_url);
            }
        } else {
            $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            logActivity(0, 'failed_login', "محاولة دخول فاشلة: $username");
        }
    }
}

// التحقق من رسائل URL
if (isset($_GET['registered'])) {
    $success_message = 'تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول';
}

if (isset($_GET['timeout'])) {
    $error_message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى';
}

// تحديد اللغة
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'ar';
$dir = $lang === 'ar' ? 'rtl' : 'ltr';

$texts = [
    'ar' => [
        'title' => 'تسجيل الدخول - حكيم',
        'login' => 'تسجيل الدخول',
        'username' => 'اسم المستخدم أو البريد الإلكتروني',
        'password' => 'كلمة المرور',
        'remember_me' => 'تذكرني',
        'forgot_password' => 'نسيت كلمة المرور؟',
        'no_account' => 'ليس لديك حساب؟',
        'register_now' => 'سجل الآن',
        'back_home' => 'العودة للرئيسية'
    ],
    'en' => [
        'title' => 'Login - Hakim',
        'login' => 'Login',
        'username' => 'Username or Email',
        'password' => 'Password',
        'remember_me' => 'Remember Me',
        'forgot_password' => 'Forgot Password?',
        'no_account' => 'Don\'t have an account?',
        'register_now' => 'Register Now',
        'back_home' => 'Back to Home'
    ]
];

$t = $texts[$lang];
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $t['title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #60a5fa;
            --success-color: #16a34a;
            --text-white: #ffffff;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-700: #334155;
            --gray-900: #0f172a;
        }

        body {
            font-family: 'Tajawal', 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            animation: gradient-shift 8s ease-in-out infinite;
        }

        @keyframes gradient-shift {
            0%, 100% {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            }
            50% {
                background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%);
            }
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.08)"/></svg>');
            background-size: 80px 80px;
            animation: float-particles 12s ease-in-out infinite;
        }

        @keyframes float-particles {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            z-index: 2;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 28px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
            position: relative;
            max-width: 420px;
            width: 100%;
            animation: card-entrance 1s ease-out;
        }

        @keyframes card-entrance {
            0% {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            animation: gradient-flow 3s ease-in-out infinite;
        }

        @keyframes gradient-flow {
            0%, 100% {
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            }
            50% {
                background: linear-gradient(90deg, #f093fb, #667eea, #764ba2);
            }
        }

        .login-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .login-header {
            padding: 2rem 2rem 1rem;
            text-align: center;
            background: transparent;
        }

        .login-header h2 {
            color: var(--gray-900);
            font-weight: 800;
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: var(--gray-700);
            font-size: 1rem;
            margin-bottom: 0;
            font-weight: 500;
        }

        .form-control {
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-100);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            background: white;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .text-center a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .text-center a:hover {
            color: var(--primary-dark);
            transform: translateY(-1px);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @media (max-width: 576px) {
            .login-card {
                margin: 1rem;
                border-radius: 20px;
            }

            .login-header h2 {
                font-size: 1.5rem;
            }

            .login-header {
                padding: 1.5rem 1.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <!-- Header -->
                        <div class="login-header text-center p-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-md me-2"></i>
                                حكيم
                            </h2>
                            <p class="mb-0 mt-2"><?php echo $t['login']; ?></p>
                        </div>
                        
                        <!-- Body -->
                        <div class="p-4">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success_message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo $t['username']; ?>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المستخدم
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>
                                        <?php echo $t['password']; ?>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        <?php echo $t['remember_me']; ?>
                                    </label>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        <?php echo $t['login']; ?>
                                    </button>
                                </div>
                                
                                <div class="text-center">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <?php echo $t['forgot_password']; ?>
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Footer -->
                        <div class="text-center p-4 border-top">
                            <p class="mb-2"><?php echo $t['no_account']; ?></p>
                            <a href="register.php" class="btn btn-outline-primary">
                                <?php echo $t['register_now']; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-right me-2"></i>
                            <?php echo $t['back_home']; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // التحقق من النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // تركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
    </script>
</body>
</html>

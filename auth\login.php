<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    redirect('../dashboard/index.php');
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "SELECT u.*, c.name as clinic_name, c.is_active as clinic_active 
                  FROM users u 
                  LEFT JOIN clinics c ON u.clinic_id = c.id 
                  WHERE (u.username = :username OR u.email = :username) AND u.is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if ($user && verifyPassword($password, $user['password'])) {
            // التحقق من حالة العيادة
            if ($user['clinic_id'] && !$user['clinic_active']) {
                $error_message = 'العيادة غير نشطة. يرجى التواصل مع الإدارة';
            } else {
                // تسجيل الدخول بنجاح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['clinic_id'] = $user['clinic_id'];
                $_SESSION['clinic_name'] = $user['clinic_name'];
                
                // تسجيل النشاط
                logActivity($user['id'], 'login', 'تسجيل دخول ناجح');
                
                // تذكر المستخدم
                if ($remember_me) {
                    $token = generateRandomToken();
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    
                    // حفظ التوكن في قاعدة البيانات
                    $update_query = "UPDATE users SET remember_token = :token WHERE id = :user_id";
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->bindParam(':token', $token);
                    $update_stmt->bindParam(':user_id', $user['id']);
                    $update_stmt->execute();
                }
                
                // إعادة التوجيه
                $redirect_url = $_GET['redirect'] ?? '../dashboard/index.php';
                redirect($redirect_url);
            }
        } else {
            $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            logActivity(0, 'failed_login', "محاولة دخول فاشلة: $username");
        }
    }
}

// التحقق من رسائل URL
if (isset($_GET['registered'])) {
    $success_message = 'تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول';
}

if (isset($_GET['timeout'])) {
    $error_message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى';
}

// تحديد اللغة
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'ar';
$dir = $lang === 'ar' ? 'rtl' : 'ltr';

$texts = [
    'ar' => [
        'title' => 'تسجيل الدخول - حكيم',
        'login' => 'تسجيل الدخول',
        'username' => 'اسم المستخدم أو البريد الإلكتروني',
        'password' => 'كلمة المرور',
        'remember_me' => 'تذكرني',
        'forgot_password' => 'نسيت كلمة المرور؟',
        'no_account' => 'ليس لديك حساب؟',
        'register_now' => 'سجل الآن',
        'back_home' => 'العودة للرئيسية'
    ],
    'en' => [
        'title' => 'Login - Hakim',
        'login' => 'Login',
        'username' => 'Username or Email',
        'password' => 'Password',
        'remember_me' => 'Remember Me',
        'forgot_password' => 'Forgot Password?',
        'no_account' => 'Don\'t have an account?',
        'register_now' => 'Register Now',
        'back_home' => 'Back to Home'
    ]
];

$t = $texts[$lang];
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $t['title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            color: white;
            border-radius: 20px 20px 0 0;
        }
    </style>
</head>
<body>
    <div class="login-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <!-- Header -->
                        <div class="login-header text-center p-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-md me-2"></i>
                                حكيم
                            </h2>
                            <p class="mb-0 mt-2"><?php echo $t['login']; ?></p>
                        </div>
                        
                        <!-- Body -->
                        <div class="p-4">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success_message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo $t['username']; ?>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المستخدم
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>
                                        <?php echo $t['password']; ?>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        <?php echo $t['remember_me']; ?>
                                    </label>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        <?php echo $t['login']; ?>
                                    </button>
                                </div>
                                
                                <div class="text-center">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <?php echo $t['forgot_password']; ?>
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Footer -->
                        <div class="text-center p-4 border-top">
                            <p class="mb-2"><?php echo $t['no_account']; ?></p>
                            <a href="register.php" class="btn btn-outline-primary">
                                <?php echo $t['register_now']; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-right me-2"></i>
                            <?php echo $t['back_home']; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // التحقق من النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // تركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
    </script>
</body>
</html>

<?php
/**
 * خادم التطوير المحلي لنظام حكيم
 * يمكن تشغيله باستخدام: php serve.php
 */

$host = 'localhost';
$port = 8000;
$docroot = __DIR__;

// التحقق من توفر المنفذ
$socket = @fsockopen($host, $port, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "❌ المنفذ $port مستخدم بالفعل\n";
    echo "جرب منفذ آخر: php -S $host:8001\n";
    exit(1);
}

echo "🚀 بدء تشغيل خادم التطوير...\n";
echo "📍 العنوان: http://$host:$port\n";
echo "📁 المجلد: $docroot\n";
echo "⏹️  للإيقاف: اضغط Ctrl+C\n";
echo str_repeat("-", 50) . "\n";

// تشغيل الخادم
$command = "php -S $host:$port -t $docroot";
passthru($command);
?>

// ملف JavaScript الرئيسي لموقع حكيم

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// دالة تهيئة التطبيق
function initializeApp() {
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة النوافذ المنبثقة
    initializeModals();
    
    // تهيئة التحقق من النماذج
    initializeFormValidation();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة التقويم
    initializeCalendar();
    
    // تهيئة الإشعارات
    initializeNotifications();
    
    // تهيئة البحث
    initializeSearch();
    
    // تهيئة الشريط الجانبي
    initializeSidebar();
}

// تهيئة التلميحات
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة النوافذ المنبثقة
function initializeModals() {
    // إضافة تأثيرات للنوافذ المنبثقة
    document.querySelectorAll('.modal').forEach(function(modal) {
        modal.addEventListener('show.bs.modal', function() {
            this.querySelector('.modal-dialog').style.transform = 'scale(0.8)';
            this.querySelector('.modal-dialog').style.opacity = '0';
            
            setTimeout(() => {
                this.querySelector('.modal-dialog').style.transform = 'scale(1)';
                this.querySelector('.modal-dialog').style.opacity = '1';
                this.querySelector('.modal-dialog').style.transition = 'all 0.3s ease';
            }, 10);
        });
    });
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    // التحقق من النماذج باستخدام Bootstrap
    var forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // التحقق من كلمة المرور
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    if (passwordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value !== passwordInput.value) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    }
}

// تهيئة الجداول
function initializeTables() {
    // إضافة وظائف البحث والترتيب للجداول
    document.querySelectorAll('.data-table').forEach(function(table) {
        // إضافة البحث
        const searchInput = table.parentElement.querySelector('.table-search');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterTable(table, this.value);
            });
        }
        
        // إضافة الترتيب
        table.querySelectorAll('th[data-sort]').forEach(function(header) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this.dataset.sort);
            });
        });
    });
}

// دالة تصفية الجدول
function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// دالة ترتيب الجدول
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.dataset.sort === column);
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return parseFloat(aValue) - parseFloat(bValue);
        }
        
        return aValue.localeCompare(bValue, 'ar');
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// تهيئة التقويم
function initializeCalendar() {
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        // هنا يمكن إضافة مكتبة تقويم مثل FullCalendar
        loadCalendarEvents();
    }
}

// تحميل أحداث التقويم
function loadCalendarEvents() {
    // جلب المواعيد من الخادم
    fetch('api/appointments.php')
        .then(response => response.json())
        .then(data => {
            displayCalendarEvents(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل المواعيد:', error);
        });
}

// عرض أحداث التقويم
function displayCalendarEvents(events) {
    // تنفيذ عرض الأحداث في التقويم
    console.log('أحداث التقويم:', events);
}

// تهيئة الإشعارات
function initializeNotifications() {
    // جلب الإشعارات غير المقروءة
    loadNotifications();
    
    // تحديث الإشعارات كل 30 ثانية
    setInterval(loadNotifications, 30000);
}

// تحميل الإشعارات
function loadNotifications() {
    fetch('api/notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.length);
            displayNotifications(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل الإشعارات:', error);
        });
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    }
}

// عرض الإشعارات
function displayNotifications(notifications) {
    const container = document.querySelector('.notifications-container');
    if (container) {
        container.innerHTML = '';
        
        notifications.forEach(notification => {
            const notificationEl = createNotificationElement(notification);
            container.appendChild(notificationEl);
        });
    }
}

// إنشاء عنصر إشعار
function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = 'notification-item p-3 border-bottom';
    div.innerHTML = `
        <div class="d-flex justify-content-between">
            <div>
                <h6 class="mb-1">${notification.title}</h6>
                <p class="mb-1 text-muted">${notification.message}</p>
                <small class="text-muted">${formatDate(notification.created_at)}</small>
            </div>
            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(${notification.id})">
                <i class="fas fa-check"></i>
            </button>
        </div>
    `;
    return div;
}

// تحديد الإشعار كمقروء
function markAsRead(notificationId) {
    fetch('api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_read',
            id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث الإشعار:', error);
    });
}

// تهيئة البحث
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(function() {
            performSearch(this.value, this.dataset.target);
        }, 300));
    });
}

// دالة البحث
function performSearch(query, target) {
    if (query.length < 2) return;
    
    fetch(`api/search.php?q=${encodeURIComponent(query)}&target=${target}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data, target);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function displaySearchResults(results, target) {
    const container = document.querySelector(`#${target}-results`);
    if (container) {
        container.innerHTML = '';
        
        results.forEach(result => {
            const resultEl = createSearchResultElement(result, target);
            container.appendChild(resultEl);
        });
    }
}

// إنشاء عنصر نتيجة بحث
function createSearchResultElement(result, target) {
    const div = document.createElement('div');
    div.className = 'search-result-item p-2 border-bottom cursor-pointer';
    div.onclick = () => selectSearchResult(result, target);
    
    switch (target) {
        case 'patients':
            div.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-user me-2"></i>
                    <div>
                        <div class="fw-bold">${result.first_name} ${result.last_name}</div>
                        <small class="text-muted">${result.phone}</small>
                    </div>
                </div>
            `;
            break;
        case 'medications':
            div.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-pills me-2"></i>
                    <div>
                        <div class="fw-bold">${result.name}</div>
                        <small class="text-muted">${result.dosage}</small>
                    </div>
                </div>
            `;
            break;
    }
    
    return div;
}

// اختيار نتيجة البحث
function selectSearchResult(result, target) {
    // تنفيذ اختيار النتيجة حسب النوع
    console.log('تم اختيار:', result, target);
}

// تهيئة الشريط الجانبي
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
}

// دوال مساعدة

// دالة التأخير (Debounce)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // أقل من دقيقة
        return 'الآن';
    } else if (diff < 3600000) { // أقل من ساعة
        return Math.floor(diff / 60000) + ' دقيقة';
    } else if (diff < 86400000) { // أقل من يوم
        return Math.floor(diff / 3600000) + ' ساعة';
    } else {
        return date.toLocaleDateString('ar-SA');
    }
}

// عرض رسالة تأكيد
function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    showToast(message, 'success');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    showToast(message, 'error');
}

// عرض Toast
function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // إزالة Toast بعد إخفائه
    toast.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// إنشاء حاوية Toast
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// تحميل البيانات بـ AJAX
function loadData(url, callback) {
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => callback(data))
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showErrorMessage('حدث خطأ في تحميل البيانات');
        });
}

// إرسال البيانات بـ AJAX
function sendData(url, data, callback) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => callback(data))
    .catch(error => {
        console.error('خطأ في إرسال البيانات:', error);
        showErrorMessage('حدث خطأ في إرسال البيانات');
    });
}

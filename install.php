<?php
// ملف تثبيت قاعدة البيانات لموقع حكيم
require_once 'config/database.php';

// التحقق من وجود قاعدة البيانات
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
        
        // إنشاء الجداول
        echo "<h3>🔧 إنشاء الجداول...</h3>";
        if ($database->createTables()) {
            echo "<p>✅ تم إنشاء جميع الجداول بنجاح</p>";
            
            // إدراج بيانات تجريبية
            echo "<h3>📊 إدراج البيانات التجريبية...</h3>";
            insertSampleData($db);
            
            echo "<h2>🎉 تم تثبيت النظام بنجاح!</h2>";
            echo "<p><a href='index.php' class='btn btn-primary'>الذهاب للموقع</a></p>";
            
        } else {
            echo "<p>❌ فشل في إنشاء الجداول</p>";
        }
    } else {
        echo "<h2>❌ فشل في الاتصال بقاعدة البيانات</h2>";
        echo "<p>يرجى التحقق من إعدادات قاعدة البيانات في ملف config/database.php</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في التثبيت</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
}

function insertSampleData($db) {
    try {
        // إنشاء عيادة تجريبية
        $clinic_query = "INSERT INTO clinics (name, address, phone, email, license_number, subscription_plan, subscription_expires, is_active) 
                        VALUES ('عيادة الدكتور أحمد', 'رام الله - فلسطين', '+970-2-1234567', '<EMAIL>', 'LIC-001', 'pro', DATE_ADD(NOW(), INTERVAL 30 DAY), 1)";
        $db->exec($clinic_query);
        $clinic_id = $db->lastInsertId();
        echo "<p>✅ تم إنشاء العيادة التجريبية</p>";
        
        // إنشاء مستخدم إداري
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $admin_query = "INSERT INTO users (username, email, password, full_name, phone, role, clinic_id, is_active) 
                       VALUES ('admin', '<EMAIL>', '$admin_password', 'مدير النظام', '+970-599-123456', 'admin', $clinic_id, 1)";
        $db->exec($admin_query);
        $admin_id = $db->lastInsertId();
        echo "<p>✅ تم إنشاء المستخدم الإداري (admin / admin123)</p>";
        
        // إنشاء طبيب
        $doctor_password = password_hash('doctor123', PASSWORD_DEFAULT);
        $doctor_query = "INSERT INTO users (username, email, password, full_name, phone, role, clinic_id, is_active) 
                        VALUES ('doctor', '<EMAIL>', '$doctor_password', 'د. محمد أحمد', '+970-599-654321', 'doctor', $clinic_id, 1)";
        $db->exec($doctor_query);
        $doctor_id = $db->lastInsertId();
        echo "<p>✅ تم إنشاء حساب الطبيب (doctor / doctor123)</p>";
        
        // إنشاء سكرتيرة
        $secretary_password = password_hash('secretary123', PASSWORD_DEFAULT);
        $secretary_query = "INSERT INTO users (username, email, password, full_name, phone, role, clinic_id, is_active) 
                           VALUES ('secretary', '<EMAIL>', '$secretary_password', 'فاطمة محمود', '+970-599-987654', 'secretary', $clinic_id, 1)";
        $db->exec($secretary_query);
        $secretary_id = $db->lastInsertId();
        echo "<p>✅ تم إنشاء حساب السكرتيرة (secretary / secretary123)</p>";
        
        // إنشاء مرضى تجريبيين
        $patients_data = [
            ['أحمد', 'محمد', '1985-05-15', 'male', '+970-599-111111', '<EMAIL>', 'رام الله', '123456789'],
            ['فاطمة', 'علي', '1990-08-22', 'female', '+970-599-222222', '<EMAIL>', 'نابلس', '987654321'],
            ['محمود', 'خالد', '1978-12-10', 'male', '+970-599-333333', '<EMAIL>', 'الخليل', '456789123'],
            ['سارة', 'أحمد', '1995-03-18', 'female', '+970-599-444444', '<EMAIL>', 'بيت لحم', '789123456'],
            ['عمر', 'يوسف', '1982-07-25', 'male', '+970-599-555555', '<EMAIL>', 'جنين', '321654987']
        ];
        
        foreach ($patients_data as $index => $patient) {
            $patient_number = 'P' . str_pad($clinic_id, 3, '0', STR_PAD_LEFT) . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $patient_query = "INSERT INTO patients (clinic_id, patient_number, first_name, last_name, date_of_birth, gender, phone, email, address, national_id, is_active) 
                             VALUES ($clinic_id, '$patient_number', '{$patient[0]}', '{$patient[1]}', '{$patient[2]}', '{$patient[3]}', '{$patient[4]}', '{$patient[5]}', '{$patient[6]}', '{$patient[7]}', 1)";
            $db->exec($patient_query);
        }
        echo "<p>✅ تم إنشاء 5 مرضى تجريبيين</p>";
        
        // إنشاء مواعيد تجريبية
        $appointments_data = [
            ['1', $doctor_id, date('Y-m-d'), '09:00:00', 'فحص دوري'],
            ['2', $doctor_id, date('Y-m-d'), '10:00:00', 'متابعة علاج'],
            ['3', $doctor_id, date('Y-m-d', strtotime('+1 day')), '09:30:00', 'استشارة طبية'],
            ['4', $doctor_id, date('Y-m-d', strtotime('+1 day')), '11:00:00', 'فحص شامل'],
            ['5', $doctor_id, date('Y-m-d', strtotime('+2 days')), '10:30:00', 'متابعة']
        ];
        
        foreach ($appointments_data as $appointment) {
            $appointment_query = "INSERT INTO appointments (clinic_id, patient_id, doctor_id, appointment_date, appointment_time, status, reason, created_by) 
                                 VALUES ($clinic_id, {$appointment[0]}, {$appointment[1]}, '{$appointment[2]}', '{$appointment[3]}', 'scheduled', '{$appointment[4]}', $admin_id)";
            $db->exec($appointment_query);
        }
        echo "<p>✅ تم إنشاء 5 مواعيد تجريبية</p>";
        
        // إنشاء زيارات تجريبية
        $visits_data = [
            ['1', $doctor_id, date('Y-m-d', strtotime('-1 day')), '09:00:00', 'صداع مستمر', 'فحص عام', 'صداع توتري', 'راحة ومسكنات', 150.00],
            ['2', $doctor_id, date('Y-m-d', strtotime('-2 days')), '10:30:00', 'ألم في المعدة', 'فحص البطن', 'التهاب معدة', 'دواء للمعدة', 200.00]
        ];
        
        foreach ($visits_data as $visit) {
            $visit_query = "INSERT INTO visits (clinic_id, patient_id, doctor_id, visit_date, visit_time, chief_complaint, physical_examination, diagnosis, treatment_plan, visit_cost, payment_status) 
                           VALUES ($clinic_id, {$visit[0]}, {$visit[1]}, '{$visit[2]}', '{$visit[3]}', '{$visit[4]}', '{$visit[5]}', '{$visit[6]}', '{$visit[7]}', {$visit[8]}, 'paid')";
            $db->exec($visit_query);
        }
        echo "<p>✅ تم إنشاء زيارتين تجريبيتين</p>";
        
        // إنشاء فواتير تجريبية
        $invoice_number1 = 'INV-' . date('Y') . '-' . str_pad($clinic_id, 3, '0', STR_PAD_LEFT) . '-0001';
        $invoice_number2 = 'INV-' . date('Y') . '-' . str_pad($clinic_id, 3, '0', STR_PAD_LEFT) . '-0002';
        
        $invoice_query1 = "INSERT INTO invoices (clinic_id, patient_id, invoice_number, invoice_date, subtotal, total_amount, paid_amount, status, payment_method, created_by) 
                          VALUES ($clinic_id, 1, '$invoice_number1', CURDATE(), 150.00, 150.00, 150.00, 'paid', 'cash', $admin_id)";
        $db->exec($invoice_query1);
        $invoice_id1 = $db->lastInsertId();
        
        $invoice_query2 = "INSERT INTO invoices (clinic_id, patient_id, invoice_number, invoice_date, subtotal, total_amount, paid_amount, status, payment_method, created_by) 
                          VALUES ($clinic_id, 2, '$invoice_number2', CURDATE(), 200.00, 200.00, 0.00, 'sent', 'cash', $admin_id)";
        $db->exec($invoice_query2);
        $invoice_id2 = $db->lastInsertId();
        
        // إضافة عناصر الفواتير
        $item_query1 = "INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price) 
                       VALUES ($invoice_id1, 'استشارة طبية', 1, 150.00, 150.00)";
        $db->exec($item_query1);
        
        $item_query2 = "INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price) 
                       VALUES ($invoice_id2, 'فحص شامل', 1, 200.00, 200.00)";
        $db->exec($item_query2);
        
        echo "<p>✅ تم إنشاء فاتورتين تجريبيتين</p>";
        
        // إنشاء إشعارات ترحيبية
        $welcome_notifications = [
            [$admin_id, 'مرحباً بك في حكيم', 'تم تثبيت النظام بنجاح. يمكنك الآن البدء في استخدام جميع المميزات.'],
            [$doctor_id, 'حساب طبيب جديد', 'تم إنشاء حسابك كطبيب. يمكنك الآن إدارة المرضى والمواعيد.'],
            [$secretary_id, 'حساب سكرتيرة جديد', 'تم إنشاء حسابك كسكرتيرة. يمكنك الآن مساعدة في إدارة العيادة.']
        ];
        
        foreach ($welcome_notifications as $notification) {
            $notif_query = "INSERT INTO notifications (clinic_id, user_id, type, title, message, is_read) 
                           VALUES ($clinic_id, {$notification[0]}, 'system_alert', '{$notification[1]}', '{$notification[2]}', 0)";
            $db->exec($notif_query);
        }
        echo "<p>✅ تم إنشاء الإشعارات الترحيبية</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ خطأ في إدراج البيانات التجريبية: " . $e->getMessage() . "</p>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام حكيم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e40af 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        .btn-primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-card">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-user-md me-2"></i>
                    تثبيت نظام حكيم
                </h1>
                <p class="text-muted">نظام إدارة العيادات الذكي</p>
            </div>
            
            <div class="installation-log">
                <!-- سيتم عرض نتائج التثبيت هنا -->
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 400" width="500" height="400">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="250" cy="200" r="180" fill="url(#bgGradient)" opacity="0.3"/>
  
  <!-- Medical Building -->
  <g transform="translate(250, 200)">
    <!-- Building Base -->
    <rect x="-80" y="-20" width="160" height="120" rx="8" fill="#ffffff" stroke="#e5e7eb" stroke-width="2"/>
    
    <!-- Building Roof -->
    <polygon points="-90,-30 0,-60 90,-30" fill="url(#primaryGradient)"/>
    
    <!-- Medical Cross on Roof -->
    <rect x="-8" y="-50" width="16" height="4" fill="#ffffff"/>
    <rect x="-2" y="-56" width="4" height="16" fill="#ffffff"/>
    
    <!-- Windows -->
    <rect x="-65" y="-5" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-35" y="-5" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-5" y="-5" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="25" y="-5" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="55" y="-5" width="20" height="15" rx="2" fill="#dbeafe"/>
    
    <rect x="-65" y="20" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-35" y="20" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-5" y="20" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="25" y="20" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="55" y="20" width="20" height="15" rx="2" fill="#dbeafe"/>
    
    <rect x="-65" y="45" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-35" y="45" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="-5" y="45" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="25" y="45" width="20" height="15" rx="2" fill="#dbeafe"/>
    <rect x="55" y="45" width="20" height="15" rx="2" fill="#dbeafe"/>
    
    <!-- Main Entrance -->
    <rect x="-15" y="70" width="30" height="30" rx="4" fill="url(#primaryGradient)"/>
    <rect x="-10" y="75" width="20" height="20" rx="2" fill="#ffffff" opacity="0.3"/>
    
    <!-- Entrance Steps -->
    <rect x="-20" y="95" width="40" height="5" fill="#e5e7eb"/>
    
    <!-- Medical Sign -->
    <circle cx="0" cy="50" r="12" fill="url(#secondaryGradient)"/>
    <rect x="-6" y="47" width="12" height="2" fill="#ffffff"/>
    <rect x="-1" y="42" width="2" height="12" fill="#ffffff"/>
  </g>
  
  <!-- Floating Medical Icons -->
  <g opacity="0.6">
    <!-- Stethoscope -->
    <g transform="translate(120, 100)">
      <circle cx="0" cy="0" r="8" fill="url(#primaryGradient)"/>
      <path d="M 0 8 Q -10 20 -15 35 Q -20 45 -10 50 Q 0 45 5 35 Q 10 20 0 8" 
            stroke="url(#primaryGradient)" stroke-width="3" fill="none"/>
      <circle cx="-10" cy="50" r="4" fill="url(#primaryGradient)"/>
    </g>
    
    <!-- Heart -->
    <g transform="translate(380, 120)">
      <path d="M 0 15 C 0 5, 10 -5, 20 5 C 30 -5, 40 5, 40 15 C 40 25, 20 45, 20 45 C 20 45, 0 25, 0 15 Z" 
            fill="url(#secondaryGradient)"/>
    </g>
    
    <!-- Medical Bag -->
    <g transform="translate(100, 280)">
      <rect x="0" y="5" width="30" height="20" rx="4" fill="url(#primaryGradient)"/>
      <rect x="10" y="0" width="10" height="8" rx="2" fill="url(#primaryGradient)"/>
      <rect x="12" y="12" width="6" height="2" fill="#ffffff"/>
      <rect x="14" y="8" width="2" height="10" fill="#ffffff"/>
    </g>
    
    <!-- Pills -->
    <g transform="translate(370, 280)">
      <ellipse cx="0" cy="0" rx="8" ry="4" fill="url(#secondaryGradient)"/>
      <ellipse cx="20" cy="5" rx="6" ry="3" fill="#f59e0b"/>
      <ellipse cx="10" cy="-8" rx="5" ry="2.5" fill="#ef4444"/>
    </g>
    
    <!-- Medical Chart -->
    <g transform="translate(80, 180)">
      <rect x="0" y="0" width="25" height="30" rx="2" fill="#ffffff" stroke="url(#primaryGradient)" stroke-width="2"/>
      <line x1="5" y1="8" x2="20" y2="8" stroke="url(#primaryGradient)" stroke-width="1"/>
      <line x1="5" y1="13" x2="15" y2="13" stroke="url(#primaryGradient)" stroke-width="1"/>
      <line x1="5" y1="18" x2="18" y2="18" stroke="url(#primaryGradient)" stroke-width="1"/>
      <line x1="5" y1="23" x2="12" y2="23" stroke="url(#primaryGradient)" stroke-width="1"/>
    </g>
    
    <!-- Thermometer -->
    <g transform="translate(400, 180)">
      <rect x="0" y="0" width="3" height="25" rx="1.5" fill="#e5e7eb"/>
      <circle cx="1.5" cy="30" r="5" fill="#ef4444"/>
      <rect x="0.5" y="5" width="2" height="15" fill="#ef4444"/>
    </g>
  </g>
  
  <!-- Technology Elements -->
  <g opacity="0.4">
    <!-- Digital Screen -->
    <g transform="translate(150, 320)">
      <rect x="0" y="0" width="40" height="25" rx="3" fill="#1f2937"/>
      <rect x="3" y="3" width="34" height="19" rx="1" fill="#10b981"/>
      <line x1="8" y1="8" x2="32" y2="8" stroke="#ffffff" stroke-width="1"/>
      <line x1="8" y1="12" x2="28" y2="12" stroke="#ffffff" stroke-width="1"/>
      <line x1="8" y1="16" x2="30" y2="16" stroke="#ffffff" stroke-width="1"/>
    </g>
    
    <!-- WiFi Signal -->
    <g transform="translate(320, 320)">
      <path d="M 0 15 Q -10 5 -20 15" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
      <path d="M 0 15 Q -7 8 -14 15" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
      <path d="M 0 15 Q -4 11 -8 15" stroke="url(#primaryGradient)" stroke-width="2" fill="none"/>
      <circle cx="0" cy="15" r="2" fill="url(#primaryGradient)"/>
    </g>
    
    <!-- Cloud -->
    <g transform="translate(200, 80)">
      <ellipse cx="0" cy="0" rx="15" ry="8" fill="#ffffff" opacity="0.8"/>
      <ellipse cx="-8" cy="-2" rx="8" ry="6" fill="#ffffff" opacity="0.8"/>
      <ellipse cx="8" cy="-2" rx="8" ry="6" fill="#ffffff" opacity="0.8"/>
      <ellipse cx="0" cy="-5" rx="6" ry="4" fill="#ffffff" opacity="0.8"/>
    </g>
  </g>
  
  <!-- Decorative Dots -->
  <g opacity="0.3">
    <circle cx="50" cy="50" r="3" fill="url(#primaryGradient)"/>
    <circle cx="450" cy="60" r="2" fill="url(#secondaryGradient)"/>
    <circle cx="70" cy="350" r="4" fill="#f59e0b"/>
    <circle cx="430" cy="340" r="3" fill="#ef4444"/>
    <circle cx="60" cy="200" r="2" fill="url(#primaryGradient)"/>
    <circle cx="440" cy="220" r="2" fill="url(#secondaryGradient)"/>
  </g>
  
  <!-- Animated Elements -->
  <g>
    <!-- Pulse Animation for Medical Cross -->
    <circle cx="250" cy="150" r="8" fill="url(#secondaryGradient)" opacity="0.6">
      <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Floating Animation for Icons -->
    <g transform="translate(120, 100)">
      <animateTransform attributeName="transform" type="translate" 
                        values="120,100; 120,95; 120,100" dur="3s" repeatCount="indefinite"/>
    </g>
    
    <g transform="translate(380, 120)">
      <animateTransform attributeName="transform" type="translate" 
                        values="380,120; 380,115; 380,120" dur="2.5s" repeatCount="indefinite"/>
    </g>
  </g>
</svg>

const path = require('path');

module.exports = {
    entry: {
        main: './assets/js/main.js',
        dashboard: './assets/js/dashboard.js',
        patients: './assets/js/patients.js',
        appointments: './assets/js/appointments.js'
    },
    output: {
        path: path.resolve(__dirname, 'assets/dist'),
        filename: '[name].bundle.js',
        clean: true
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            },
            {
                test: /\.css$/i,
                use: ['style-loader', 'css-loader']
            },
            {
                test: /\.(png|svg|jpg|jpeg|gif)$/i,
                type: 'asset/resource'
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/resource'
            }
        ]
    },
    optimization: {
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all'
                }
            }
        }
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'assets/js'),
            '@css': path.resolve(__dirname, 'assets/css'),
            '@images': path.resolve(__dirname, 'assets/images')
        }
    }
};

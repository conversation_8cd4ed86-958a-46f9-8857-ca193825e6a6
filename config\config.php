<?php
// إعدادات عامة للموقع
define('SITE_NAME', 'حكيم - نظام إدارة العيادات');
define('SITE_URL', 'https://xyz.collectandwin.xyz');
define('SITE_EMAIL', '<EMAIL>');

// إعدادات الأمان
define('ENCRYPTION_KEY', 'hakim_clinic_2024_secure_key');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// إعدادات العملة
define('CURRENCY_SYMBOL', '₪');
define('CURRENCY_CODE', 'ILS');

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Jerusalem');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الاشتراكات
define('SUBSCRIPTION_PLANS', [
    'basic' => [
        'name_ar' => 'الخطة الأساسية',
        'name_en' => 'Basic Plan',
        'price' => 99,
        'features' => [
            'patients' => 100,
            'users' => 2,
            'storage' => '1GB'
        ]
    ],
    'pro' => [
        'name_ar' => 'الخطة المتقدمة',
        'name_en' => 'Pro Plan',
        'price' => 199,
        'features' => [
            'patients' => 500,
            'users' => 5,
            'storage' => '5GB'
        ]
    ],
    'enterprise' => [
        'name_ar' => 'خطة المؤسسات',
        'name_en' => 'Enterprise Plan',
        'price' => 399,
        'features' => [
            'patients' => -1, // غير محدود
            'users' => -1,
            'storage' => '20GB'
        ]
    ]
]);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات الدفع
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live

// رسائل النظام
define('MESSAGES', [
    'ar' => [
        'success' => [
            'login' => 'تم تسجيل الدخول بنجاح',
            'logout' => 'تم تسجيل الخروج بنجاح',
            'register' => 'تم إنشاء الحساب بنجاح',
            'update' => 'تم التحديث بنجاح',
            'delete' => 'تم الحذف بنجاح',
            'save' => 'تم الحفظ بنجاح'
        ],
        'error' => [
            'login_failed' => 'فشل في تسجيل الدخول',
            'access_denied' => 'ليس لديك صلاحية للوصول',
            'invalid_data' => 'البيانات المدخلة غير صحيحة',
            'file_upload_failed' => 'فشل في رفع الملف',
            'database_error' => 'خطأ في قاعدة البيانات'
        ]
    ],
    'en' => [
        'success' => [
            'login' => 'Login successful',
            'logout' => 'Logout successful',
            'register' => 'Account created successfully',
            'update' => 'Updated successfully',
            'delete' => 'Deleted successfully',
            'save' => 'Saved successfully'
        ],
        'error' => [
            'login_failed' => 'Login failed',
            'access_denied' => 'Access denied',
            'invalid_data' => 'Invalid data entered',
            'file_upload_failed' => 'File upload failed',
            'database_error' => 'Database error'
        ]
    ]
]);

// تضمين ملف قاعدة البيانات
require_once 'database.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// فحص انتهاء الجلسة
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
    session_unset();
    session_destroy();
    header('Location: ' . SITE_URL . '/auth/login.php?timeout=1');
    exit();
}
$_SESSION['last_activity'] = time();

// دالة للحصول على اللغة الحالية
function getCurrentLanguage() {
    return isset($_SESSION['language']) ? $_SESSION['language'] : 'ar';
}

// دالة لتعيين اللغة
function setLanguage($lang) {
    if (in_array($lang, ['ar', 'en'])) {
        $_SESSION['language'] = $lang;
    }
}

// دالة للحصول على رسالة بلغة محددة
function getMessage($type, $key, $lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    return MESSAGES[$lang][$type][$key] ?? '';
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = null) {
    if ($format === null) {
        $format = DATE_FORMAT;
    }
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

// دالة لتنسيق المبلغ
function formatCurrency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY_SYMBOL;
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من قوة كلمة المرور
function isStrongPassword($password) {
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لتوليد رمز عشوائي
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// دالة لتنظيف البيانات المدخلة
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة للتحقق من CSRF Token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لتوليد CSRF Token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateRandomToken();
    }
    return $_SESSION['csrf_token'];
}
?>

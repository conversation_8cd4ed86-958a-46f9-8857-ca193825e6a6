# ملفات النظام
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات PHP
*.log
.env
config/local.php

# مجلدات التبعيات
/vendor/
/node_modules/
/bower_components/

# ملفات التجميع
/assets/dist/
/assets/css/style.css.map
/coverage/
/build/

# ملفات المستخدمين المرفوعة
/uploads/*
!/uploads/.gitkeep

# ملفات النسخ الاحتياطي
/backups/*
!/backups/.gitkeep

# ملفات السجلات
/logs/*
!/logs/.gitkeep
*.log

# ملفات مؤقتة
/tmp/*
!/tmp/.gitkeep
/cache/*
!/cache/.gitkeep

# ملفات قاعدة البيانات
*.sql
*.sqlite
*.db

# ملفات التكوين الحساسة
config/database.local.php
config/mail.local.php
config/payment.local.php

# ملفات الاختبار
/tests/coverage/
phpunit.xml

# ملفات Composer
composer.phar
composer.lock

# ملفات NPM
package-lock.json
yarn.lock

# ملفات Sass
.sass-cache/
*.css.map

# ملفات الإنتاج
.htaccess.production
robots.txt.production

# ملفات الأمان
*.pem
*.key
*.crt
*.csr

# ملفات التوثيق
/docs/build/

# ملفات المحرر
*.sublime-project
*.sublime-workspace

# ملفات Windows
*.bat
*.cmd

# ملفات macOS
.AppleDouble
.LSOverride

# ملفات Linux
*~

# ملفات التطوير المحلي
.local/
.development/

# ملفات الاختبار المحلي
.phpunit.result.cache

# ملفات التحليل
.phpstan.neon
.psalm.xml

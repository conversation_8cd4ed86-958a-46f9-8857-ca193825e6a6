/* ملف الأنماط الرئيسي لموقع حكيم */

/* الخطوط العربية المحسنة */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap');

/* المتغيرات العامة - نظام ألوان طبي احترافي */
:root {
    /* الألوان الأساسية - نظام طبي متناسق */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --primary-lighter: #dbeafe;
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);

    /* الألوان الثانوية - أخضر طبي */
    --secondary-color: #16a34a;
    --secondary-dark: #15803d;
    --secondary-light: #4ade80;
    --secondary-lighter: #dcfce7;
    --secondary-gradient: linear-gradient(135deg, #16a34a 0%, #15803d 100%);

    /* ألوان الحالة */
    --success-color: #16a34a;
    --warning-color: #ea580c;
    --danger-color: #dc2626;
    --info-color: #0ea5e9;

    /* ألوان متدرجة للحالات */
    --success-gradient: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    --warning-gradient: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
    --danger-gradient: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --info-gradient: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);

    /* الألوان المحايدة */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* ألوان النصوص */
    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;

    /* ألوان الخلفيات */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-accent: #f1f5f9;
    --bg-dark: #0f172a;

    --font-family-ar: 'Tajawal', 'Almarai', 'Cairo', 'Segoe UI', sans-serif;
    --font-family-en: 'Inter', 'Segoe UI', 'Roboto', sans-serif;

    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.7;
    color: var(--gray-800);
    background-color: var(--light-color);
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* دعم اللغة العربية */
[dir="rtl"] {
    text-align: right;
    font-family: var(--font-family-ar);
}

[dir="ltr"] {
    text-align: left;
    font-family: var(--font-family-en);
}

/* تحسين النصوص العربية */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-ar);
    font-weight: 700;
    line-height: 1.3;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.7;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--text-muted);
    line-height: 1.6;
}

/* تحسين الألوان العامة */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-dark {
    color: var(--text-primary) !important;
}

.text-light {
    color: var(--text-light) !important;
}

.text-white {
    color: var(--white) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* الخلفيات المتدرجة المحسنة */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: var(--white);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%);
    color: var(--white);
}

.bg-gradient-medical {
    background: linear-gradient(135deg, #1e40af 0%, #0284c7 50%, #059669 100%);
    color: var(--white);
}

/* الخلفيات الثابتة */
.bg-primary {
    background-color: var(--primary-color) !important;
    color: var(--white) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
    color: var(--white) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
    color: var(--white) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
    color: var(--white) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
    color: var(--white) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
    color: var(--white) !important;
}

.bg-light {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

.bg-dark {
    background-color: var(--gray-900) !important;
    color: var(--white) !important;
}

.bg-white {
    background-color: var(--white) !important;
    color: var(--text-primary) !important;
}

/* الأزرار المحسنة مع أنيميشن احترافي */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.875rem 2rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    font-family: var(--font-family-ar);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn i {
    margin-left: 0.75rem;
    font-size: 1rem;
    transition: transform 0.3s ease;
}

[dir="ltr"] .btn i {
    margin-right: 0.75rem;
    margin-left: 0;
}

.btn:hover i {
    transform: scale(1.1);
}

/* الأزرار الأساسية */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    border: 2px solid transparent;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
    color: var(--text-white);
}

.btn-primary:active {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background-color: transparent;
    position: relative;
}

.btn-outline-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-outline-primary:hover::after {
    width: 100%;
}

.btn-outline-primary:hover {
    color: var(--text-white);
    border-color: var(--primary-color);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.3);
}

/* أزرار النجاح */
.btn-success {
    background: var(--success-gradient);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(22, 163, 74, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--secondary-dark) 0%, #166534 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(22, 163, 74, 0.4);
    color: var(--text-white);
}

/* أزرار التحذير */
.btn-warning {
    background: var(--warning-gradient);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(234, 88, 12, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #c2410c 0%, #9a3412 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(234, 88, 12, 0.4);
    color: var(--text-white);
}

/* أزرار الخطر */
.btn-danger {
    background: var(--danger-gradient);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(220, 38, 38, 0.4);
    color: var(--text-white);
}

/* أزرار المعلومات */
.btn-info {
    background: var(--info-gradient);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(14, 165, 233, 0.4);
    color: var(--text-white);
}

/* الأزرار الفاتحة */
.btn-light {
    background: var(--white);
    border: 2px solid var(--gray-300);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.btn-light:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--text-primary);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.12);
}

/* الأزرار الداكنة */
.btn-dark {
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(15, 23, 42, 0.3);
}

.btn-dark:hover {
    background: linear-gradient(135deg, var(--gray-900) 0%, #000000 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(15, 23, 42, 0.4);
    color: var(--text-white);
}

/* أحجام الأزرار */
.btn-lg {
    padding: 1.125rem 2.5rem;
    font-size: 1.25rem;
    border-radius: 16px;
    letter-spacing: 0.75px;
}

.btn-sm {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    border-radius: 8px;
}

/* تأثيرات إضافية للأزرار */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
}

.btn:active {
    transform: translateY(-2px) scale(1.01);
}

/* أنيميشن النبض للأزرار المهمة */
.btn-pulse {
    animation: pulse-btn 2s infinite;
}

@keyframes pulse-btn {
    0% {
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5), 0 0 0 10px rgba(37, 99, 235, 0.1);
    }
    100% {
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    }
}

/* شريط التنقل المحسن */
.navbar {
    backdrop-filter: blur(15px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1030;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.75rem;
    color: var(--primary-color) !important;
    font-family: var(--font-family-ar);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    transition: var(--transition);
    color: var(--gray-700) !important;
    border-radius: var(--border-radius-sm);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
}

/* القسم الرئيسي المحسن مع ألوان متناسقة */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, #1e3a8a 100%);
    padding-top: 80px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroGrid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23heroGrid)"/></svg>');
    background-size: 40px 40px;
    opacity: 0.4;
    animation: float 6s ease-in-out infinite;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(16, 185, 129, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.2) 0%, transparent 50%);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    color: var(--white) !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    font-weight: 800;
    line-height: 1.2;
}

.hero-section h2 {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.hero-section p {
    color: rgba(255, 255, 255, 0.85) !important;
    font-size: 1.25rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    line-height: 1.6;
}

.hero-section .btn {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-section .btn:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
    transform: translateY(-3px);
}

.min-vh-75 {
    min-height: 75vh;
}

/* بطاقات المميزات المحسنة مع ألوان متناسقة */
.feature-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(30, 64, 175, 0.15);
    border-color: var(--primary-light);
}

/* أيقونات المميزات مع ألوان متدرجة */
.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: var(--transition);
    position: relative;
}

/* ألوان مختلفة لكل أيقونة */
.feature-card:nth-child(1) .feature-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.feature-card:nth-child(2) .feature-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.feature-card:nth-child(3) .feature-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.feature-card:nth-child(4) .feature-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.feature-card:nth-child(5) .feature-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.feature-card:nth-child(6) .feature-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: var(--white);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.feature-card h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.375rem;
}

.feature-card p {
    color: var(--text-muted);
    line-height: 1.7;
    margin-bottom: 0;
    font-size: 1rem;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* البطاقات المحسنة */
.card {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-900);
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1.5rem;
}

/* خطط الاشتراك المحسنة مع ألوان متناسقة */
.pricing-card {
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--gray-200);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
    background: var(--white);
    height: 100%;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 20px 50px rgba(30, 64, 175, 0.2);
    position: relative;
}

.pricing-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.05), rgba(29, 78, 216, 0.05));
    z-index: 1;
}

.pricing-card.featured > * {
    position: relative;
    z-index: 2;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
    box-shadow: 0 30px 60px rgba(30, 64, 175, 0.25);
}

.pricing-card .card-header {
    text-align: center;
    padding: 2.5rem 2rem;
    position: relative;
    background: var(--white);
}

.pricing-card.featured .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.pricing-card .card-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.pricing-card.featured .card-header h4 {
    color: var(--white);
}

.pricing-card .card-header .display-6 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1;
    margin: 1rem 0 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pricing-card.featured .card-header .display-6 {
    background: var(--white);
    -webkit-background-clip: text;
    -webkit-text-fill-color: var(--white);
    background-clip: text;
    color: var(--white);
}

.pricing-card .card-header small {
    opacity: 0.7;
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--text-muted);
}

.pricing-card.featured .card-header small {
    color: rgba(255, 255, 255, 0.9);
}

.pricing-card .badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--white);
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    animation: pulse 2s infinite;
}

.pricing-card .card-body {
    padding: 2.5rem 2rem;
    background: var(--white);
}

.pricing-card .card-body ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-card .card-body li {
    padding: 1rem 0;
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-size: 1rem;
    transition: var(--transition);
}

.pricing-card .card-body li:last-child {
    border-bottom: none;
}

.pricing-card .card-body li:hover {
    color: var(--text-primary);
    transform: translateX(5px);
}

.pricing-card .card-body li i {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--success-color), #047857);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 0.75rem;
    flex-shrink: 0;
}

[dir="ltr"] .pricing-card .card-body li i {
    margin-right: 1rem;
    margin-left: 0;
}

.pricing-card .card-footer {
    padding: 2rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--gray-200);
}

.pricing-card.featured .card-footer {
    background: rgba(30, 64, 175, 0.05);
}

/* تحسينات إضافية للخطط */
.pricing-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

.pricing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(30,64,175,0.1)"/></svg>');
    background-size: 50px 50px;
    opacity: 0.3;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

/* الحاويات */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, #1e40af 100%);
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    transition: var(--transition);
}

[dir="ltr"] .sidebar {
    left: 0;
    right: auto;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border-radius: 0;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 0.5rem;
}

[dir="ltr"] .sidebar-nav .nav-link i {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 250px;
    padding: 2rem;
    transition: var(--transition);
}

[dir="ltr"] .main-content {
    margin-left: 250px;
    margin-right: 0;
}

/* الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: #6b7280;
    font-weight: 500;
}

/* التقويم */
.calendar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.calendar-day {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    min-height: 80px;
    transition: var(--transition);
}

.calendar-day:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

.calendar-day.today {
    background-color: rgba(44, 90, 160, 0.1);
    font-weight: 600;
}

/* الأنيميشن المحسن */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.8s ease-out;
}

.animate-zoomIn {
    animation: zoomIn 0.6s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.8s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* تأثيرات التمرير */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    [dir="ltr"] .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        margin-left: 0;
        padding: 1rem;
    }
    
    .hero-section {
        padding: 2rem 0;
        min-height: auto;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* طباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Footer محسن مع ألوان واضحة ومتناسقة */
footer {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--bg-dark) 100%) !important;
    color: var(--text-white);
    position: relative;
    overflow: hidden;
    padding: 3rem 0 1rem;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><circle cx="30" cy="30" r="1.5" fill="rgba(37,99,235,0.1)"/></svg>');
    background-size: 60px 60px;
    z-index: 1;
}

footer .container {
    position: relative;
    z-index: 2;
}

footer h5, footer h6 {
    color: var(--text-white) !important;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    font-size: 1.25rem;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

footer p {
    color: #cbd5e1 !important;
    line-height: 1.8;
    margin-bottom: 1rem;
    font-size: 1rem;
}

footer a {
    color: #e2e8f0 !important;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

footer a:hover {
    color: var(--primary-light) !important;
    transform: translateX(8px);
}

footer a::before {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 0;
    height: 2px;
    background: var(--primary-light);
    transition: width 0.3s ease;
}

footer a:hover::before {
    width: 100%;
}

footer .list-unstyled li {
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    padding: 0.25rem 0;
}

footer .list-unstyled li:hover {
    transform: translateX(8px);
    background: rgba(37, 99, 235, 0.1);
    border-radius: 6px;
    padding-right: 0.5rem;
}

/* أيقونات وسائل التواصل الاجتماعي محسنة */
footer .d-flex a {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 0 0.5rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

footer .d-flex a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

footer .d-flex a:hover::before {
    opacity: 1;
}

footer .d-flex a i {
    position: relative;
    z-index: 2;
    font-size: 1.25rem;
    color: var(--text-white);
    transition: transform 0.3s ease;
}

footer .d-flex a:hover {
    transform: translateY(-8px) scale(1.15);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
    border-color: var(--primary-light);
}

footer .d-flex a:hover i {
    transform: scale(1.1);
}

/* تحسينات إضافية للفوتر */
footer .row {
    align-items: flex-start;
}

footer hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    margin: 2.5rem 0 1.5rem;
}

footer .text-muted {
    color: #94a3b8 !important;
    font-size: 0.95rem;
}

/* تحسين النصوص في Footer */
footer .col-lg-4 h5 {
    color: var(--text-white) !important;
    margin-bottom: 1.5rem;
}

footer .col-lg-2 h6,
footer .col-lg-3 h6 {
    color: var(--text-white) !important;
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
}

/* أقسام إضافية */
.section-padding {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 3rem;
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: var(--border-radius);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.cursor-pointer {
    cursor: pointer;
}

.shadow-custom {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* تأثيرات إضافية */
.typewriter {
    border-right: 2px solid var(--primary-color);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { border-color: var(--primary-color); }
    51%, 100% { border-color: transparent; }
}

.counter {
    font-weight: 700;
    color: var(--primary-color);
}

.progress-bar {
    transition: width 1.5s ease-in-out;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* تأثيرات التمرير المتقدمة */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.sticky-top-custom {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }

    .navbar.scrolled {
        padding: 0.25rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-section {
        padding: 4rem 0 2rem;
        min-height: 80vh;
    }

    .hero-section h1 {
        font-size: 2.5rem !important;
    }

    .pricing-card.featured {
        transform: none;
        margin: 1rem 0;
    }

    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .stats-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem !important;
    }

    .hero-section h2 {
        font-size: 1.25rem !important;
    }

    .hero-section p {
        font-size: 1rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .pricing-card .card-header .display-6 {
        font-size: 2.5rem;
    }

    .section-padding {
        padding: 3rem 0;
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .btn,
    .no-print,
    footer {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-section * {
        color: black !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .pricing-card {
        margin-bottom: 2rem;
    }
}

/* تحسينات الوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-100: #1f2937;
        --gray-200: #374151;
        --light-color: #111827;
    }

    body {
        background-color: var(--light-color);
        color: #f9fafb;
    }

    .navbar {
        background-color: rgba(31, 41, 55, 0.95) !important;
    }

    .card {
        background-color: var(--gray-800);
        border-color: var(--gray-700);
        color: #f9fafb;
    }
}

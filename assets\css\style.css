/* ملف الأنماط الرئيسي لموقع حكيم */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* المتغيرات العامة */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #34d399;
    --accent-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    --font-family-ar: 'Cairo', 'Amiri', sans-serif;
    --font-family-en: 'Inter', 'Segoe UI', sans-serif;
    
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

/* دعم اللغة العربية */
[dir="rtl"] {
    text-align: right;
}

[dir="ltr"] {
    text-align: left;
    font-family: var(--font-family-en);
}

/* الخلفيات المتدرجة */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #059669 100%);
}

.bg-gradient-accent {
    background: linear-gradient(135deg, var(--accent-color) 0%, #d97706 100%);
}

/* الألوان المخصصة */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: translateY(-2px);
}

/* شريط التنقل */
.navbar {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

/* القسم الرئيسي */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.min-vh-75 {
    min-height: 75vh;
}

/* بطاقات المميزات */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* البطاقات */
.card {
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

/* الحاويات */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, #1e40af 100%);
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    transition: var(--transition);
}

[dir="ltr"] .sidebar {
    left: 0;
    right: auto;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border-radius: 0;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 0.5rem;
}

[dir="ltr"] .sidebar-nav .nav-link i {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 250px;
    padding: 2rem;
    transition: var(--transition);
}

[dir="ltr"] .main-content {
    margin-left: 250px;
    margin-right: 0;
}

/* الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: #6b7280;
    font-weight: 500;
}

/* التقويم */
.calendar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.calendar-day {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    min-height: 80px;
    transition: var(--transition);
}

.calendar-day:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

.calendar-day.today {
    background-color: rgba(44, 90, 160, 0.1);
    font-weight: 600;
}

/* الأنيميشن */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    [dir="ltr"] .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        margin-left: 0;
        padding: 1rem;
    }
    
    .hero-section {
        padding: 2rem 0;
        min-height: auto;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* طباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.cursor-pointer {
    cursor: pointer;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e40af;
}

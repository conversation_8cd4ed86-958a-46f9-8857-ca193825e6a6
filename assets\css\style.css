/* ملف الأنماط الرئيسي لموقع حكيم */

/* الخطوط العربية المحسنة */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap');

/* المتغيرات العامة */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --font-family-ar: 'Tajawal', 'Almarai', 'Cairo', 'Segoe UI', sans-serif;
    --font-family-en: 'Inter', 'Segoe UI', 'Roboto', sans-serif;

    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.7;
    color: var(--gray-800);
    background-color: var(--light-color);
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* دعم اللغة العربية */
[dir="rtl"] {
    text-align: right;
    font-family: var(--font-family-ar);
}

[dir="ltr"] {
    text-align: left;
    font-family: var(--font-family-en);
}

/* تحسين النصوص العربية */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-ar);
    font-weight: 700;
    line-height: 1.3;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

p {
    color: var(--gray-700);
    margin-bottom: 1rem;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--gray-600);
}

/* الخلفيات المتدرجة */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #059669 100%);
}

.bg-gradient-accent {
    background: linear-gradient(135deg, var(--accent-color) 0%, #d97706 100%);
}

/* الألوان المخصصة */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-muted {
    color: var(--gray-500) !important;
}

.text-dark {
    color: var(--gray-900) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.bg-light {
    background-color: var(--gray-100) !important;
}

.bg-dark {
    background-color: var(--gray-900) !important;
    color: white !important;
}

/* الأزرار المحسنة */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: 2px solid transparent;
    font-family: var(--font-family-ar);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
    color: white;
    transform: translateY(-2px);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
    color: white;
    transform: translateY(-2px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* شريط التنقل المحسن */
.navbar {
    backdrop-filter: blur(15px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1030;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.75rem;
    color: var(--primary-color) !important;
    font-family: var(--font-family-ar);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    transition: var(--transition);
    color: var(--gray-700) !important;
    border-radius: var(--border-radius-sm);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
}

/* القسم الرئيسي المحسن */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, #1e3a8a 100%);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    background-size: 50px 50px;
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section h2 {
    color: rgba(255, 255, 255, 0.9) !important;
}

.hero-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1.125rem;
}

.min-vh-75 {
    min-height: 75vh;
}

/* بطاقات المميزات المحسنة */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    transition: var(--transition);
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    color: var(--secondary-color);
}

.feature-card h4 {
    color: var(--gray-900);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* البطاقات المحسنة */
.card {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-900);
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1.5rem;
}

/* خطط الاشتراك المحسنة */
.pricing-card {
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--gray-200);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
    background: white;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: var(--box-shadow-lg);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.pricing-card .card-header {
    text-align: center;
    padding: 2rem 1.5rem;
    position: relative;
}

.pricing-card.featured .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.pricing-card .card-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.pricing-card .card-header .display-6 {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    margin: 1rem 0 0.5rem;
}

.pricing-card .card-header small {
    opacity: 0.8;
    font-size: 1rem;
}

.pricing-card .badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--warning-color);
    color: white;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
}

.pricing-card .card-body {
    padding: 2rem 1.5rem;
}

.pricing-card .card-body ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-card .card-body li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    color: var(--gray-700);
}

.pricing-card .card-body li:last-child {
    border-bottom: none;
}

.pricing-card .card-body li i {
    color: var(--success-color);
    margin-left: 0.75rem;
    font-size: 1.125rem;
}

.pricing-card .card-footer {
    padding: 1.5rem;
    background: transparent;
    border-top: 1px solid var(--gray-200);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

/* الحاويات */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, #1e40af 100%);
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    transition: var(--transition);
}

[dir="ltr"] .sidebar {
    left: 0;
    right: auto;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border-radius: 0;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 0.5rem;
}

[dir="ltr"] .sidebar-nav .nav-link i {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: 250px;
    padding: 2rem;
    transition: var(--transition);
}

[dir="ltr"] .main-content {
    margin-left: 250px;
    margin-right: 0;
}

/* الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: #6b7280;
    font-weight: 500;
}

/* التقويم */
.calendar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.calendar-day {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    min-height: 80px;
    transition: var(--transition);
}

.calendar-day:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

.calendar-day.today {
    background-color: rgba(44, 90, 160, 0.1);
    font-weight: 600;
}

/* الأنيميشن المحسن */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.8s ease-out;
}

.animate-zoomIn {
    animation: zoomIn 0.6s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.8s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* تأثيرات التمرير */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    [dir="ltr"] .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        margin-left: 0;
        padding: 1rem;
    }
    
    .hero-section {
        padding: 2rem 0;
        min-height: auto;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* طباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Footer محسن */
footer {
    background: var(--gray-900) !important;
    color: white;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    z-index: 1;
}

footer .container {
    position: relative;
    z-index: 2;
}

footer h5, footer h6 {
    color: white !important;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

footer p {
    color: var(--gray-400) !important;
    line-height: 1.6;
}

footer a {
    color: var(--gray-400) !important;
    text-decoration: none;
    transition: var(--transition);
}

footer a:hover {
    color: var(--primary-light) !important;
    transform: translateX(5px);
}

footer .list-unstyled li {
    margin-bottom: 0.75rem;
}

footer .d-flex a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

footer .d-flex a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    color: white !important;
}

/* أقسام إضافية */
.section-padding {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 3rem;
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: var(--border-radius);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.cursor-pointer {
    cursor: pointer;
}

.shadow-custom {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* تأثيرات إضافية */
.typewriter {
    border-right: 2px solid var(--primary-color);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { border-color: var(--primary-color); }
    51%, 100% { border-color: transparent; }
}

.counter {
    font-weight: 700;
    color: var(--primary-color);
}

.progress-bar {
    transition: width 1.5s ease-in-out;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* تأثيرات التمرير المتقدمة */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.sticky-top-custom {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }

    .navbar.scrolled {
        padding: 0.25rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-section {
        padding: 4rem 0 2rem;
        min-height: 80vh;
    }

    .hero-section h1 {
        font-size: 2.5rem !important;
    }

    .pricing-card.featured {
        transform: none;
        margin: 1rem 0;
    }

    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .stats-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem !important;
    }

    .hero-section h2 {
        font-size: 1.25rem !important;
    }

    .hero-section p {
        font-size: 1rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .pricing-card .card-header .display-6 {
        font-size: 2.5rem;
    }

    .section-padding {
        padding: 3rem 0;
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .btn,
    .no-print,
    footer {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-section * {
        color: black !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .pricing-card {
        margin-bottom: 2rem;
    }
}

/* تحسينات الوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-100: #1f2937;
        --gray-200: #374151;
        --light-color: #111827;
    }

    body {
        background-color: var(--light-color);
        color: #f9fafb;
    }

    .navbar {
        background-color: rgba(31, 41, 55, 0.95) !important;
    }

    .card {
        background-color: var(--gray-800);
        border-color: var(--gray-700);
        color: #f9fafb;
    }
}

<?php
session_start();
require_once '../config/config.php';
require_once '../includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    logActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
    
    // حذف remember token إذا كان موجوداً
    if (isset($_COOKIE['remember_token'])) {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "UPDATE users SET remember_token = NULL WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        
        // حذف الكوكي
        setcookie('remember_token', '', time() - 3600, '/');
    }
}

// تنظيف الجلسة
session_unset();
session_destroy();

// إعادة التوجيه لصفحة تسجيل الدخول
redirect('login.php?logout=1');
?>

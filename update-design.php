<?php
/**
 * ملف تحديث التصميم لموقع حكيم
 * يقوم بتطبيق جميع التحسينات الجديدة
 */

echo "🎨 بدء تحديث تصميم موقع حكيم...\n\n";

// فحص الملفات المطلوبة
$required_files = [
    'assets/css/style.css',
    'assets/css/fonts.css',
    'assets/js/main.js',
    'index.php'
];

$missing_files = [];
foreach ($required_files as $file) {
    if (!file_exists($file)) {
        $missing_files[] = $file;
    }
}

if (!empty($missing_files)) {
    echo "❌ الملفات التالية مفقودة:\n";
    foreach ($missing_files as $file) {
        echo "   - $file\n";
    }
    exit(1);
}

echo "✅ جميع الملفات المطلوبة موجودة\n\n";

// فحص أحجام الملفات
$file_sizes = [];
foreach ($required_files as $file) {
    $size = filesize($file);
    $file_sizes[$file] = $size;
    echo "📄 $file: " . formatBytes($size) . "\n";
}

echo "\n";

// فحص التحديثات المطبقة
echo "🔍 فحص التحديثات المطبقة:\n\n";

// فحص ملف CSS
$css_content = file_get_contents('assets/css/style.css');
$css_checks = [
    'Tajawal' => strpos($css_content, 'Tajawal') !== false,
    'pricing-card' => strpos($css_content, 'pricing-card') !== false,
    'scroll-reveal' => strpos($css_content, 'scroll-reveal') !== false,
    'animate-float' => strpos($css_content, 'animate-float') !== false,
    'typewriter' => strpos($css_content, 'typewriter') !== false
];

echo "📝 ملف CSS:\n";
foreach ($css_checks as $feature => $exists) {
    echo "   " . ($exists ? "✅" : "❌") . " $feature\n";
}

// فحص ملف JavaScript
$js_content = file_get_contents('assets/js/main.js');
$js_checks = [
    'initializeScrollAnimations' => strpos($js_content, 'initializeScrollAnimations') !== false,
    'initializeVisualEffects' => strpos($js_content, 'initializeVisualEffects') !== false,
    'createParticleEffect' => strpos($js_content, 'createParticleEffect') !== false,
    'typewriterEffect' => strpos($js_content, 'typewriterEffect') !== false,
    'animateCounter' => strpos($js_content, 'animateCounter') !== false
];

echo "\n📝 ملف JavaScript:\n";
foreach ($js_checks as $feature => $exists) {
    echo "   " . ($exists ? "✅" : "❌") . " $feature\n";
}

// فحص ملف الخطوط
if (file_exists('assets/css/fonts.css')) {
    $fonts_content = file_get_contents('assets/css/fonts.css');
    $fonts_checks = [
        'Tajawal' => strpos($fonts_content, 'Tajawal') !== false,
        'Almarai' => strpos($fonts_content, 'Almarai') !== false,
        'Cairo' => strpos($fonts_content, 'Cairo') !== false,
        'arabic-text' => strpos($fonts_content, 'arabic-text') !== false
    ];

    echo "\n📝 ملف الخطوط:\n";
    foreach ($fonts_checks as $feature => $exists) {
        echo "   " . ($exists ? "✅" : "❌") . " $feature\n";
    }
} else {
    echo "\n❌ ملف الخطوط غير موجود\n";
}

// فحص ملف index.php
$index_content = file_get_contents('index.php');
$index_checks = [
    'fonts.css' => strpos($index_content, 'fonts.css') !== false,
    'pricing-card' => strpos($index_content, 'pricing-card') !== false,
    'AOS.init' => strpos($index_content, 'AOS.init') !== false,
    'typewriter' => strpos($index_content, 'typewriter') !== false
];

echo "\n📝 ملف index.php:\n";
foreach ($index_checks as $feature => $exists) {
    echo "   " . ($exists ? "✅" : "❌") . " $feature\n";
}

echo "\n";

// إحصائيات التحديث
$total_checks = count($css_checks) + count($js_checks) + count($fonts_checks ?? []) + count($index_checks);
$passed_checks = array_sum($css_checks) + array_sum($js_checks) + array_sum($fonts_checks ?? []) + array_sum($index_checks);

echo "📊 إحصائيات التحديث:\n";
echo "   ✅ تم تطبيق: $passed_checks من $total_checks\n";
echo "   📈 نسبة الإنجاز: " . round(($passed_checks / $total_checks) * 100, 1) . "%\n\n";

// التوصيات
echo "💡 التوصيات:\n";

if ($passed_checks == $total_checks) {
    echo "   🎉 ممتاز! تم تطبيق جميع التحديثات بنجاح\n";
    echo "   🚀 الموقع جاهز للاستخدام مع التصميم المحسن\n";
} else {
    echo "   ⚠️  بعض التحديثات لم يتم تطبيقها بالكامل\n";
    echo "   🔧 يرجى مراجعة الملفات والتأكد من التحديثات\n";
}

echo "\n";

// معلومات إضافية
echo "ℹ️  معلومات إضافية:\n";
echo "   📱 التصميم متجاوب للجوال\n";
echo "   🎨 خطوط عربية محسنة (Tajawal, Almarai, Cairo)\n";
echo "   ✨ أنيميشن متقدم مع AOS\n";
echo "   🎯 تأثيرات بصرية تفاعلية\n";
echo "   💰 خطط اشتراك محسنة\n";
echo "   🔄 أنيميشن التمرير والعدادات\n";
echo "   🎪 تأثيرات الجسيمات في الخلفية\n\n";

echo "✅ تم الانتهاء من فحص التحديثات!\n";
echo "🌐 يمكنك الآن زيارة الموقع لرؤية التحسينات\n";

// دالة تنسيق حجم الملف
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث تصميم موقع حكيم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .update-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        .status-success {
            color: #10b981;
        }
        .status-error {
            color: #ef4444;
        }
        .status-warning {
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-card">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-palette me-2"></i>
                    تحديث تصميم موقع حكيم
                </h1>
                <p class="text-muted">فحص وتطبيق التحسينات الجديدة</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>✨ التحسينات المطبقة:</h4>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>خطوط عربية محسنة</li>
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>تصميم خطط الاشتراك</li>
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>أنيميشن متقدم</li>
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>تأثيرات بصرية</li>
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>تحسين الألوان</li>
                        <li class="mb-2"><i class="fas fa-check-circle status-success me-2"></i>شريط تنقل ديناميكي</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h4>🎯 المميزات الجديدة:</h4>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>تأثير الكتابة</li>
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>عدادات متحركة</li>
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>جسيمات الخلفية</li>
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>تمرير سلس</li>
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>تحسين الاستجابة</li>
                        <li class="mb-2"><i class="fas fa-star status-warning me-2"></i>تحسين الأداء</li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="text-center">
                <h4>🚀 الخطوات التالية</h4>
                <div class="d-flex justify-content-center gap-3 flex-wrap mt-3">
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>
                        عرض الموقع
                    </a>
                    <a href="start.php" class="btn btn-success btn-lg">
                        <i class="fas fa-cog me-2"></i>
                        فحص النظام
                    </a>
                    <a href="install.php" class="btn btn-info btn-lg">
                        <i class="fas fa-database me-2"></i>
                        إعداد قاعدة البيانات
                    </a>
                </div>
            </div>
            
            <div class="mt-4 p-3 bg-light rounded">
                <h5>📝 ملاحظات:</h5>
                <ul class="mb-0">
                    <li>تم تحسين الخطوط العربية لتكون أكثر وضوحاً</li>
                    <li>تم إصلاح مشكلة الألوان في جميع أقسام الموقع</li>
                    <li>تم تحسين تصميم خطط الاشتراك مع أنيميشن جذاب</li>
                    <li>تم إضافة تأثيرات بصرية متقدمة للتفاعل</li>
                    <li>الموقع الآن متجاوب بالكامل مع جميع الأجهزة</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * ملف بدء تشغيل موقع حكيم
 * يقوم بفحص المتطلبات وإعداد النظام
 */

// فحص إصدار PHP
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die('خطأ: يتطلب النظام PHP 7.4 أو أحدث. الإصدار الحالي: ' . PHP_VERSION);
}

// فحص الامتدادات المطلوبة
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'curl', 'openssl', 'json'];
$missing_extensions = [];

foreach ($required_extensions as $extension) {
    if (!extension_loaded($extension)) {
        $missing_extensions[] = $extension;
    }
}

if (!empty($missing_extensions)) {
    die('خطأ: الامتدادات التالية مطلوبة: ' . implode(', ', $missing_extensions));
}

// فحص الملفات المطلوبة
$required_files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'assets/css/style.css',
    'assets/js/main.js'
];

$missing_files = [];
foreach ($required_files as $file) {
    if (!file_exists($file)) {
        $missing_files[] = $file;
    }
}

if (!empty($missing_files)) {
    die('خطأ: الملفات التالية مفقودة: ' . implode(', ', $missing_files));
}

// فحص صلاحيات المجلدات
$writable_dirs = ['uploads', 'assets'];
$non_writable = [];

foreach ($writable_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    if (!is_writable($dir)) {
        $non_writable[] = $dir;
    }
}

if (!empty($non_writable)) {
    echo '<div class="alert alert-warning">تحذير: المجلدات التالية غير قابلة للكتابة: ' . implode(', ', $non_writable) . '</div>';
}

// محاولة الاتصال بقاعدة البيانات
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    // فحص وجود الجداول
    $tables_query = "SHOW TABLES";
    $stmt = $db->query($tables_query);
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_tables = ['users', 'clinics', 'patients', 'appointments', 'visits', 'prescriptions', 'invoices'];
    $missing_tables = array_diff($required_tables, $tables);
    
    if (!empty($missing_tables)) {
        echo '<div class="alert alert-info">يبدو أن قاعدة البيانات غير مُعدة. <a href="install.php" class="btn btn-primary">تشغيل التثبيت</a></div>';
    } else {
        echo '<div class="alert alert-success">✅ النظام جاهز للعمل! <a href="index.php" class="btn btn-success">الذهاب للموقع</a></div>';
    }
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
    echo '<div class="alert alert-info">يرجى تشغيل التثبيت أولاً. <a href="install.php" class="btn btn-primary">تشغيل التثبيت</a></div>';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بدء تشغيل نظام حكيم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e40af 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .start-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        .status-item {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .status-item.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .status-item.warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .status-item.success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="start-card">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-user-md me-2"></i>
                    نظام حكيم
                </h1>
                <p class="text-muted">فحص حالة النظام وبدء التشغيل</p>
            </div>
            
            <div class="system-status">
                <h3>حالة النظام</h3>
                
                <div class="status-item success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?> ✅
                </div>
                
                <div class="status-item success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>الامتدادات المطلوبة:</strong> جميعها متوفرة ✅
                </div>
                
                <div class="status-item success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>الملفات الأساسية:</strong> موجودة ✅
                </div>
                
                <?php if (!empty($non_writable)): ?>
                <div class="status-item warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> بعض المجلدات غير قابلة للكتابة
                </div>
                <?php else: ?>
                <div class="status-item success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>صلاحيات المجلدات:</strong> صحيحة ✅
                </div>
                <?php endif; ?>
                
                <hr>
                
                <h4>معلومات الخادم</h4>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                            <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                            <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                            <li><strong>حد رفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                            <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                        </ul>
                    </div>
                </div>
                
                <hr>
                
                <h4>الامتدادات المُحملة</h4>
                <div class="row">
                    <?php foreach ($required_extensions as $ext): ?>
                    <div class="col-md-3 mb-2">
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i><?php echo $ext; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h4>الخطوات التالية</h4>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="install.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-download me-2"></i>
                            تثبيت النظام
                        </a>
                        <a href="index.php" class="btn btn-success btn-lg">
                            <i class="fas fa-home me-2"></i>
                            الذهاب للموقع
                        </a>
                        <a href="dashboard/" class="btn btn-info btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </div>
                </div>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h5>ملاحظات مهمة:</h5>
                    <ul class="mb-0">
                        <li>تأكد من إعداد معلومات قاعدة البيانات في <code>config/database.php</code></li>
                        <li>قم بتشغيل <code>install.php</code> لإنشاء الجداول والبيانات التجريبية</li>
                        <li>غيّر كلمات المرور الافتراضية بعد التثبيت</li>
                        <li>تأكد من تفعيل HTTPS في الإنتاج</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
